const express = require('express');
const User = require('../models/User');
const { 
  requireAdmin, 
  requireAnyRole, 
  requireAdminOrOwner,
  attachUserRoles 
} = require('../middleware/auth');

const router = express.Router();

/**
 * GET /api/users
 * Listar todos los usuarios (solo admins)
 */
router.get('/', requireAdmin, async (req, res) => {
  try {
    const users = await User.getAllWithRoles();
    res.json({
      success: true,
      data: users
    });
  } catch (error) {
    console.error('Error getting users:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * GET /api/users/:userId
 * Obtener un usuario específico (admin o el mismo usuario)
 */
router.get('/:userId', requireAdminOrOwner('userId'), async (req, res) => {
  try {
    const user = await User.getById(req.params.userId);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        error: 'User not found'
      });
    }

    res.json({
      success: true,
      data: user
    });
  } catch (error) {
    console.error('Error getting user:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * POST /api/users/:userId/roles
 * Asignar un rol a un usuario (solo admins)
 */
router.post('/:userId/roles', requireAdmin, async (req, res) => {
  try {
    const { role } = req.body;
    
    if (!role) {
      return res.status(400).json({
        success: false,
        error: 'Role is required'
      });
    }

    await User.assignRole(req.params.userId, role);
    
    const updatedUser = await User.getById(req.params.userId);
    
    res.json({
      success: true,
      message: `Role '${role}' assigned successfully`,
      data: updatedUser
    });
  } catch (error) {
    console.error('Error assigning role:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Internal server error'
    });
  }
});

/**
 * DELETE /api/users/:userId/roles/:role
 * Remover un rol de un usuario (solo admins)
 */
router.delete('/:userId/roles/:role', requireAdmin, async (req, res) => {
  try {
    await User.removeRole(req.params.userId, req.params.role);
    
    const updatedUser = await User.getById(req.params.userId);
    
    res.json({
      success: true,
      message: `Role '${req.params.role}' removed successfully`,
      data: updatedUser
    });
  } catch (error) {
    console.error('Error removing role:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * PUT /api/users/:userId/roles
 * Reemplazar todos los roles de un usuario (solo admins)
 */
router.put('/:userId/roles', requireAdmin, async (req, res) => {
  try {
    const { roles } = req.body;
    
    if (!Array.isArray(roles)) {
      return res.status(400).json({
        success: false,
        error: 'Roles must be an array'
      });
    }

    await User.setRoles(req.params.userId, roles);
    
    const updatedUser = await User.getById(req.params.userId);
    
    res.json({
      success: true,
      message: 'Roles updated successfully',
      data: updatedUser
    });
  } catch (error) {
    console.error('Error updating roles:', error);
    res.status(500).json({
      success: false,
      error: error.message || 'Internal server error'
    });
  }
});

/**
 * GET /api/users/:userId/permissions
 * Verificar permisos de un usuario (admin o el mismo usuario)
 */
router.get('/:userId/permissions', requireAdminOrOwner('userId'), async (req, res) => {
  try {
    const { roles: checkRoles } = req.query;
    const userId = req.params.userId;
    
    let permissions = {};
    
    if (checkRoles) {
      const rolesToCheck = checkRoles.split(',');
      
      for (const role of rolesToCheck) {
        permissions[role] = await User.hasRole(userId, role.trim());
      }
    }
    
    const userRoles = await User.getUserRoles(userId);
    
    res.json({
      success: true,
      data: {
        userId,
        roles: userRoles,
        permissions
      }
    });
  } catch (error) {
    console.error('Error checking permissions:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

module.exports = router;
