const { query, queryOne } = require('../config/database');

class User {
  /**
   * Obtener un usuario por ID incluyendo sus roles
   */
  static async getById(id) {
    const user = await queryOne(
      'SELECT id, email, name, is_active, created_at, updated_at, last_login_at FROM users WHERE id = ?',
      [id]
    );

    if (user) {
      user.roles = await this.getUserRoles(id);
    }

    return user;
  }

  /**
   * Obtener un usuario por email incluyendo sus roles
   */
  static async getByEmail(email) {
    const user = await queryOne(
      'SELECT id, email, name, is_active, created_at, updated_at, last_login_at FROM users WHERE email = ?',
      [email]
    );

    if (user) {
      user.roles = await this.getUserRoles(user.id);
    }

    return user;
  }

  /**
   * Obtener todos los roles de un usuario
   */
  static async getUserRoles(userId) {
    return await query(`
      SELECT r.id, r.slug, r.name, r.description
      FROM roles r
      INNER JOIN user_roles ur ON r.id = ur.role_id
      WHERE ur.user_id = ?
      ORDER BY r.name
    `, [userId]);
  }

  /**
   * Verificar si un usuario tiene un rol específico
   */
  static async hasRole(userId, roleSlug) {
    const result = await queryOne(`
      SELECT 1
      FROM user_roles ur
      INNER JOIN roles r ON ur.role_id = r.id
      WHERE ur.user_id = ? AND r.slug = ?
    `, [userId, roleSlug]);

    return !!result;
  }

  /**
   * Verificar si un usuario tiene alguno de los roles especificados
   */
  static async hasAnyRole(userId, roleSlugs) {
    if (!Array.isArray(roleSlugs) || roleSlugs.length === 0) {
      return false;
    }

    const placeholders = roleSlugs.map(() => '?').join(',');
    const result = await queryOne(`
      SELECT 1
      FROM user_roles ur
      INNER JOIN roles r ON ur.role_id = r.id
      WHERE ur.user_id = ? AND r.slug IN (${placeholders})
    `, [userId, ...roleSlugs]);

    return !!result;
  }

  /**
   * Verificar si un usuario tiene todos los roles especificados
   */
  static async hasAllRoles(userId, roleSlugs) {
    if (!Array.isArray(roleSlugs) || roleSlugs.length === 0) {
      return true;
    }

    const placeholders = roleSlugs.map(() => '?').join(',');
    const result = await queryOne(`
      SELECT COUNT(DISTINCT r.slug) as count
      FROM user_roles ur
      INNER JOIN roles r ON ur.role_id = r.id
      WHERE ur.user_id = ? AND r.slug IN (${placeholders})
    `, [userId, ...roleSlugs]);

    return result && result.count === roleSlugs.length;
  }

  /**
   * Asignar un rol a un usuario
   */
  static async assignRole(userId, roleSlug) {
    const role = await queryOne('SELECT id FROM roles WHERE slug = ?', [roleSlug]);
    if (!role) {
      throw new Error(`Role '${roleSlug}' not found`);
    }

    await query(`
      INSERT IGNORE INTO user_roles (user_id, role_id)
      VALUES (?, ?)
    `, [userId, role.id]);

    return true;
  }

  /**
   * Remover un rol de un usuario
   */
  static async removeRole(userId, roleSlug) {
    await query(`
      DELETE ur FROM user_roles ur
      INNER JOIN roles r ON ur.role_id = r.id
      WHERE ur.user_id = ? AND r.slug = ?
    `, [userId, roleSlug]);

    return true;
  }

  /**
   * Reemplazar todos los roles de un usuario
   */
  static async setRoles(userId, roleSlugs) {
    // Eliminar todos los roles actuales
    await query('DELETE FROM user_roles WHERE user_id = ?', [userId]);

    // Asignar los nuevos roles
    if (Array.isArray(roleSlugs) && roleSlugs.length > 0) {
      for (const roleSlug of roleSlugs) {
        await this.assignRole(userId, roleSlug);
      }
    }

    return true;
  }

  /**
   * Obtener todos los usuarios con sus roles
   */
  static async getAllWithRoles() {
    const users = await query(`
      SELECT u.id, u.email, u.name, u.is_active, u.created_at,
             GROUP_CONCAT(r.name ORDER BY r.name) as role_names,
             GROUP_CONCAT(r.slug ORDER BY r.name) as role_slugs
      FROM users u
      LEFT JOIN user_roles ur ON u.id = ur.user_id
      LEFT JOIN roles r ON ur.role_id = r.id
      GROUP BY u.id, u.email, u.name, u.is_active, u.created_at
      ORDER BY u.email
    `);

    return users.map(user => ({
      ...user,
      roles: user.role_slugs ? user.role_slugs.split(',') : [],
      role_names: user.role_names ? user.role_names.split(',') : []
    }));
  }
}

module.exports = User;
