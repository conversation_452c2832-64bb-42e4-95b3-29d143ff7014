const User = require('../models/User');

/**
 * Middleware para verificar que el usuario tenga al menos uno de los roles especificados
 * @param {string|string[]} roles - Rol o array de roles permitidos
 */
function requireAnyRole(roles) {
  const allowedRoles = Array.isArray(roles) ? roles : [roles];
  
  return async (req, res, next) => {
    try {
      // Aquí asumiríamos que ya tienes el userId del token JWT o sesión
      const userId = req.user?.id;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      const hasPermission = await User.hasAnyRole(userId, allowedRoles);
      
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          error: `Access denied. Required roles: ${allowedRoles.join(' or ')}`
        });
      }

      next();
    } catch (error) {
      console.error('Role check error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  };
}

/**
 * Middleware para verificar que el usuario tenga todos los roles especificados
 * @param {string|string[]} roles - Rol o array de roles requeridos
 */
function requireAllRoles(roles) {
  const requiredRoles = Array.isArray(roles) ? roles : [roles];
  
  return async (req, res, next) => {
    try {
      const userId = req.user?.id;
      
      if (!userId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      const hasPermission = await User.hasAllRoles(userId, requiredRoles);
      
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          error: `Access denied. Required roles: ${requiredRoles.join(' and ')}`
        });
      }

      next();
    } catch (error) {
      console.error('Role check error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  };
}

/**
 * Middleware para verificar un rol específico
 * @param {string} role - Rol requerido
 */
function requireRole(role) {
  return requireAnyRole([role]);
}

/**
 * Middleware para verificar que el usuario sea admin
 */
const requireAdmin = requireRole('admin');

/**
 * Middleware para verificar que el usuario sea admin o el propietario del recurso
 * @param {string} userIdParam - Nombre del parámetro que contiene el ID del usuario
 */
function requireAdminOrOwner(userIdParam = 'userId') {
  return async (req, res, next) => {
    try {
      const currentUserId = req.user?.id;
      const targetUserId = req.params[userIdParam];
      
      if (!currentUserId) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
      }

      // Si es el mismo usuario, permitir acceso
      if (currentUserId === targetUserId) {
        return next();
      }

      // Si no es el mismo usuario, verificar si es admin
      const isAdmin = await User.hasRole(currentUserId, 'admin');
      
      if (!isAdmin) {
        return res.status(403).json({
          success: false,
          error: 'Access denied. Admin privileges or ownership required'
        });
      }

      next();
    } catch (error) {
      console.error('Authorization error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  };
}

/**
 * Middleware para agregar información de roles al objeto req.user
 */
async function attachUserRoles(req, res, next) {
  try {
    if (req.user?.id) {
      const roles = await User.getUserRoles(req.user.id);
      req.user.roles = roles;
      req.user.roleNames = roles.map(r => r.name);
      req.user.roleSlugs = roles.map(r => r.slug);
    }
    next();
  } catch (error) {
    console.error('Error attaching user roles:', error);
    next(); // Continuar sin roles en caso de error
  }
}

module.exports = {
  requireRole,
  requireAnyRole,
  requireAllRoles,
  requireAdmin,
  requireAdminOrOwner,
  attachUserRoles
};
