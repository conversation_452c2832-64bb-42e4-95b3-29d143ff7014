{"name": "mysql2", "version": "3.15.0", "description": "fast mysql driver. Implements core protocol, prepared statements, ssl and compression in native JS", "main": "index.js", "typings": "typings/mysql/index", "type": "commonjs", "scripts": {"lint": "eslint . && prettier --check .", "lint:fix": "eslint . --fix && prettier --write .", "test": "poku -d -r=verbose --sequential test/esm test/unit test/integration", "test:bun": "bun poku -d --sequential test/esm test/unit test/integration", "test:deno": "deno run --allow-read --allow-env --allow-run npm:poku -d --sequential --denoAllow=\"read,env,net,sys\" test/esm test/unit test/integration", "test:tsc-build": "cd \"test/tsc-build\" && npx tsc -p \"tsconfig.json\"", "coverage-test": "c8 npm run test", "benchmark": "node ./benchmarks/benchmark.js", "wait-port": "wait-on"}, "repository": {"type": "git", "url": "git+https://github.com/sidorares/node-mysql2.git"}, "homepage": "https://sidorares.github.io/node-mysql2/docs", "keywords": ["mysql", "client", "server"], "files": ["lib", "typings/mysql", "index.js", "index.d.ts", "promise.js", "promise.d.ts"], "exports": {".": "./index.js", "./package.json": "./package.json", "./promise": "./promise.js", "./promise.js": "./promise.js"}, "engines": {"node": ">= 8.0"}, "author": "<PERSON><PERSON> <and<PERSON>.<EMAIL>>", "license": "MIT", "dependencies": {"aws-ssl-profiles": "^1.1.1", "denque": "^2.1.0", "generate-function": "^2.3.1", "iconv-lite": "^0.7.0", "long": "^5.2.1", "lru.min": "^1.0.0", "named-placeholders": "^1.1.3", "seq-queue": "^0.0.5", "sqlstring": "^2.3.2"}, "devDependencies": {"@eslint/eslintrc": "^3.3.0", "@eslint/js": "^9.21.0", "@eslint/markdown": "^7.0.0", "@types/node": "^24.0.0", "@typescript-eslint/eslint-plugin": "^8.26.0", "@typescript-eslint/parser": "^8.26.0", "assert-diff": "^3.0.2", "benchmark": "^2.1.4", "c8": "^10.1.1", "error-stack-parser": "^2.0.3", "eslint-config-prettier": "^10.0.2", "eslint-plugin-async-await": "^0.0.0", "eslint-plugin-markdown": "^5.1.0", "eslint-plugin-prettier": "^5.2.3", "globals": "^16.0.0", "poku": "^3.0.0", "portfinder": "^1.0.28", "prettier": "^3.0.0", "typescript": "^5.0.2"}}