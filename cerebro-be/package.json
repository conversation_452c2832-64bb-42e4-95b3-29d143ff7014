{"name": "cerebro-be", "version": "1.0.0", "description": "Backend API for Docker container status monitoring", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "build": "echo 'No build step needed for Node.js'", "migrate": "node scripts/migrate.js", "migrate:roles": "node scripts/migrate-roles.js", "db:setup": "node scripts/migrate.js", "test:db": "node scripts/test-db.js", "test:roles": "node scripts/test-roles.js"}, "dependencies": {"cors": "^2.8.5", "dockerode": "^4.0.2", "dotenv": "^17.2.2", "express": "^4.18.2", "mysql2": "^3.14.5"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["api"], "author": "MSarkNet", "license": "MIT"}