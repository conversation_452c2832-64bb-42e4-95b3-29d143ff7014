#!/usr/bin/env node

// Cargar variables de entorno
require('dotenv').config();

const { testConnection } = require('../src/config/database');
const User = require('../src/models/User');

async function testRoles() {
  console.log('🧪 Testing multiple roles system...\n');

  try {
    // 1. Probar conexión
    console.log('1️⃣ Testing database connection...');
    const connected = await testConnection();
    if (!connected) {
      throw new Error('Database connection failed');
    }
    console.log('✅ Database connection successful\n');

    // 2. Obtener usuario admin
    console.log('2️⃣ Getting admin user...');
    const admin = await User.getByEmail('<EMAIL>');
    if (!admin) {
      throw new Error('Admin user not found');
    }
    console.log(`✅ Found user: ${admin.email} (${admin.name})`);
    console.log(`   Roles: ${admin.roles.map(r => r.name).join(', ')}\n`);

    // 3. Probar verificaciones de roles
    console.log('3️⃣ Testing role checks...');
    const hasAdmin = await User.hasRole(admin.id, 'admin');
    const hasUser = await User.hasRole(admin.id, 'user');
    const hasService = await User.hasRole(admin.id, 'service');
    
    console.log(`   Has ADMIN role: ${hasAdmin ? '✅' : '❌'}`);
    console.log(`   Has USER role: ${hasUser ? '✅' : '❌'}`);
    console.log(`   Has SERVICE role: ${hasService ? '✅' : '❌'}`);

    // 4. Probar verificación de múltiples roles
    console.log('\n4️⃣ Testing multiple role checks...');
    const hasAnyAdminUser = await User.hasAnyRole(admin.id, ['admin', 'user']);
    const hasAllAdminUser = await User.hasAllRoles(admin.id, ['admin', 'user']);
    const hasAllAdminService = await User.hasAllRoles(admin.id, ['admin', 'service']);
    
    console.log(`   Has ANY of [admin, user]: ${hasAnyAdminUser ? '✅' : '❌'}`);
    console.log(`   Has ALL of [admin, user]: ${hasAllAdminUser ? '✅' : '❌'}`);
    console.log(`   Has ALL of [admin, service]: ${hasAllAdminService ? '✅' : '❌'}`);

    // 5. Probar asignación de rol
    console.log('\n5️⃣ Testing role assignment...');
    await User.assignRole(admin.id, 'service');
    const hasServiceAfter = await User.hasRole(admin.id, 'service');
    console.log(`   Assigned SERVICE role: ${hasServiceAfter ? '✅' : '❌'}`);

    // 6. Mostrar roles actuales
    const updatedAdmin = await User.getById(admin.id);
    console.log(`   Current roles: ${updatedAdmin.roles.map(r => r.name).join(', ')}`);

    // 7. Probar remoción de rol
    console.log('\n6️⃣ Testing role removal...');
    await User.removeRole(admin.id, 'service');
    const hasServiceRemoved = await User.hasRole(admin.id, 'service');
    console.log(`   Removed SERVICE role: ${!hasServiceRemoved ? '✅' : '❌'}`);

    // 8. Mostrar todos los usuarios con roles
    console.log('\n7️⃣ All users with roles:');
    const allUsers = await User.getAllWithRoles();
    allUsers.forEach(user => {
      console.log(`   - ${user.email}: [${user.role_names.join(', ')}]`);
    });

    console.log('\n🎉 All role tests passed!');

  } catch (error) {
    console.error('❌ Role test failed:', error.message);
    process.exit(1);
  }
}

// Ejecutar pruebas
if (require.main === module) {
  testRoles();
}

module.exports = { testRoles };
