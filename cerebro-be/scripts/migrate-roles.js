#!/usr/bin/env node

/**
 * Script de migración específico para actualizar la estructura de roles
 * Migra de un solo rol por usuario a múltiples roles por usuario
 */

// Cargar variables de entorno
require('dotenv').config();

const mysql = require('mysql2/promise');

// Configuración de la base de datos
const dbConfig = {
  host: process.env.DB_HOST || 'mariadb',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_DATABASE || 'msarknet',
  port: process.env.DB_PORT || 3306,
  charset: 'utf8mb4',
  timezone: '+00:00',
  multipleStatements: true
};

async function migrateRoles() {
  let connection;

  try {
    console.log('🔄 Connecting to database...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ Connected to database');

    // 1. Crear tabla user_roles si no existe
    console.log('📋 Creating user_roles table...');
    await connection.query(`
      CREATE TABLE IF NOT EXISTS user_roles (
        id         INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
        user_id    VARCHAR(255) NOT NULL,
        role_id    INT NOT NULL,
        created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        
        UNIQUE KEY unique_user_role (user_id, role_id),
        KEY idx_user_roles_user_id (user_id),
        KEY idx_user_roles_role_id (role_id),
        
        CONSTRAINT fk_user_roles_user
          FOREIGN KEY (user_id) REFERENCES users(id)
          ON DELETE CASCADE
          ON UPDATE CASCADE,
          
        CONSTRAINT fk_user_roles_role
          FOREIGN KEY (role_id) REFERENCES roles(id)
          ON DELETE CASCADE
          ON UPDATE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
    `);
    console.log('✅ user_roles table ready');

    // 2. Migrar datos existentes de users.role_id a user_roles
    console.log('🔄 Migrating existing role assignments...');
    const [existingRoles] = await connection.query(`
      SELECT id, role_id FROM users WHERE role_id IS NOT NULL
    `);

    if (existingRoles.length > 0) {
      console.log(`📊 Found ${existingRoles.length} users with roles to migrate`);
      
      for (const user of existingRoles) {
        await connection.query(`
          INSERT IGNORE INTO user_roles (user_id, role_id)
          VALUES (?, ?)
        `, [user.id, user.role_id]);
      }
      console.log('✅ Role assignments migrated');
    } else {
      console.log('ℹ️  No existing role assignments to migrate');
    }

    // 3. Eliminar la columna role_id de users
    console.log('🗑️  Removing role_id column from users table...');
    await connection.query('ALTER TABLE users DROP FOREIGN KEY IF EXISTS fk_users_role');
    await connection.query('ALTER TABLE users DROP INDEX IF EXISTS idx_users_role_id');
    await connection.query('ALTER TABLE users DROP COLUMN IF EXISTS role_id');
    console.log('✅ role_id column removed');

    // 4. Verificar la migración
    console.log('🔍 Verifying migration...');
    const [roleCount] = await connection.query('SELECT COUNT(*) as count FROM user_roles');
    console.log(`📊 Total role assignments: ${roleCount[0].count}`);

    // 5. Mostrar usuarios y sus roles
    const [userRoles] = await connection.query(`
      SELECT u.email, u.name, GROUP_CONCAT(r.name) as roles
      FROM users u
      LEFT JOIN user_roles ur ON u.id = ur.user_id
      LEFT JOIN roles r ON ur.role_id = r.id
      GROUP BY u.id, u.email, u.name
      ORDER BY u.email
    `);

    console.log('\n👥 Users and their roles:');
    userRoles.forEach(user => {
      console.log(`   - ${user.email} (${user.name || 'No name'}): ${user.roles || 'No roles'}`);
    });

    console.log('\n🎉 Role migration completed successfully!');

  } catch (error) {
    console.error('❌ Role migration failed:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

// Ejecutar migración
if (require.main === module) {
  console.log('🚀 Starting role migration...\n');
  migrateRoles();
}

module.exports = { migrateRoles };
