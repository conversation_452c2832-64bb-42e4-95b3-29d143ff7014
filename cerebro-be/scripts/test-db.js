#!/usr/bin/env node

// Cargar variables de entorno
require('dotenv').config();

const { testConnection } = require('../src/config/database');

async function testDatabase() {
  console.log('🧪 Testing database connection...\n');

  try {
    // Probar conexión
    console.log('1️⃣ Testing database connection...');
    const connected = await testConnection();
    if (!connected) {
      throw new Error('Database connection failed');
    }
    console.log('✅ Database connection successful\n');

    console.log('🎉 Database test passed!');

  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    process.exit(1);
  }
}

// Ejecutar pruebas
if (require.main === module) {
  testDatabase();
}

module.exports = { testDatabase };
