networks:
  proxy:
    external: true
    name: proxy
    driver: bridge

volumes:
  mariadb-data:

services:
  traefik:
    container_name: traefik
    image: traefik:v3.5.2
    command:
      - --api.dashboard=true # ⚠️ Solo en local
      - --api.insecure=true # ⚠️ Solo en local
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --providers.docker.network=proxy
      - --providers.file.directory=/etc/traefik/dynamic
      - --providers.file.watch=true
      - --entryPoints.web.address=:80
      - --entryPoints.websecure.address=:443
      - --entryPoints.websecure.http.tls=true
      # redirección http -> https
      - --entryPoints.web.http.redirections.entryPoint.to=websecure
      - --entryPoints.web.http.redirections.entryPoint.scheme=https
      # logs
      - --accesslog=true
      - --accesslog.filepath=/var/log/traefik/access.log
      - --log.level=INFO
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    ports:
      - 80:80
      - 443:443
      - 8080:8080
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./certs:/certs:ro
      - ./dynamic:/etc/traefik/dynamic:ro
    labels:
      - traefik.enable=true
      - traefik.http.routers.traefik.rule=Host(`traefik.msarknet.me`)
      - traefik.http.routers.traefik.entrypoints=websecure
      - traefik.http.routers.traefik.service=api@internal
      - traefik.http.routers.traefik.tls=true
      - traefik.http.routers.traefik.middlewares=secure-headers@file,auth@file
    networks:
      - proxy

  mariadb:
    container_name: mariadb
    image: mariadb:12.0.2
    restart: unless-stopped
    environment:
      MARIADB_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MARIADB_DATABASE: ${DB_DATABASE}
      MARIADB_USER: ${DB_USER}
      MARIADB_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mariadb-data:/var/lib/mysql
    networks:
      - proxy

  adminer:
    container_name: adminer
    image: adminer:latest
    restart: unless-stopped
    environment:
      ADMINER_DEFAULT_SERVER: ${DB_HOST}
    labels:
      - traefik.enable=true
      - traefik.http.routers.adminer.rule=Host(`adminer.msarknet.me`)
      - traefik.http.routers.adminer.entrypoints=websecure
      - traefik.http.routers.adminer.tls=true
      - traefik.http.routers.adminer.middlewares=secure-headers@file,auth@file
      - traefik.http.services.adminer.loadbalancer.server.port=8080
    networks:
      - proxy
    depends_on:
      - mariadb
