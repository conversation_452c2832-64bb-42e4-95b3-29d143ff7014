networks:
  proxy:
    external: true
    name: proxy

services:
  cerebro-fe:
    container_name: cerebro-fe
    build:
      context: ../cerebro-fe
      dockerfile: Dockerfile
      target: development
    restart: unless-stopped
    volumes:
      - ../cerebro-fe:/app
      - ../cerebro-fe/node_modules:/app/node_modules
    labels:
      - traefik.enable=true
      - traefik.http.routers.cerebro-fe.rule=Host(`msarknet.me`)
      - traefik.http.routers.cerebro-fe.entrypoints=websecure
      - traefik.http.routers.cerebro-fe.tls=true
      - traefik.http.routers.cerebro-fe.middlewares=spa-headers@file
      - traefik.http.services.cerebro-fe.loadbalancer.server.port=5173
    networks:
      - proxy

  cerebro-be:
    container_name: cerebro-be
    build:
      context: ../cerebro-be
      dockerfile: Dockerfile
      target: development
    restart: unless-stopped
    volumes:
      - ../cerebro-be:/app
      - ../cerebro-be/node_modules:/app/node_modules
      - /var/run/docker.sock:/var/run/docker.sock
    labels:
      - traefik.enable=true
      - traefik.http.routers.cerebro-be.rule=Host(`api.msarknet.me`)
      - traefik.http.routers.cerebro-be.entrypoints=websecure
      - traefik.http.routers.cerebro-be.tls=true
      - traefik.http.routers.cerebro-be.middlewares=secure-headers@file,cors-headers@file
      - traefik.http.services.cerebro-be.loadbalancer.server.port=5000
    networks:
      - proxy
