networks:
  proxy:
    external: true
    name: proxy

services:
  mkdocs:
    container_name: mkdocs
    build:
      context: ../mkdocs
      dockerfile: Dockerfile
    restart: unless-stopped
    volumes:
      - ../mkdocs:/app
    networks:
      - proxy
    labels:
      - traefik.enable=true
      - traefik.http.routers.mkdocs.rule=Host(`docs.msarknet.me`)
      - traefik.http.routers.mkdocs.entrypoints=websecure
      - traefik.http.routers.mkdocs.tls=true
      - traefik.http.routers.mkdocs.middlewares=secure-headers@file,compression@file
      - traefik.http.services.mkdocs.loadbalancer.server.port=8000
