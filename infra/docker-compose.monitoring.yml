networks:
  proxy:
    external: true
    name: proxy
    driver: bridge

volumes:
  portainer_data:
  grafana_data:
  prometheus_data:

services:
  portainer:
    container_name: portainer
    image: portainer/portainer-ce:latest
    restart: unless-stopped
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - portainer_data:/data
    labels:
      - traefik.enable=true
      - traefik.http.routers.portainer.rule=Host(`portainer.msarknet.me`)
      - traefik.http.routers.portainer.entrypoints=websecure
      - traefik.http.routers.portainer.tls=true
      - traefik.http.routers.portainer.middlewares=secure-headers@file
      - traefik.http.services.portainer.loadbalancer.server.port=9000
    networks:
      - proxy

  whoami:
    container_name: whoami
    image: traefik/whoami
    restart: unless-stopped
    labels:
      - traefik.enable=true
      - traefik.http.routers.whoami.rule=Host(`whoami.msarknet.me`)
      - traefik.http.routers.whoami.entrypoints=websecure
      - traefik.http.routers.whoami.tls=true
      - traefik.http.routers.whoami.middlewares=secure-headers@file,cors-headers@file,rate-limit@file
    networks:
      - proxy

  grafana:
    container_name: grafana
    image: grafana/grafana:latest
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
      - GF_SECURITY_ADMIN_EMAIL=${GRAFANA_ADMIN_EMAIL}
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
    labels:
      - traefik.enable=true
      - traefik.http.routers.grafana.rule=Host(`grafana.msarknet.me`)
      - traefik.http.routers.grafana.entrypoints=websecure
      - traefik.http.routers.grafana.tls=true
      - traefik.http.routers.grafana.middlewares=secure-headers@file
      - traefik.http.services.grafana.loadbalancer.server.port=3000
    networks:
      - proxy

  prometheus:
    container_name: prometheus
    image: prom/prometheus:latest
    restart: unless-stopped
    networks:
      - proxy
    volumes:
      - prometheus_data:/prometheus
      - ./dynamic/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - --config.file=/etc/prometheus/prometheus.yml
      - --storage.tsdb.path=/prometheus
      - --web.console.libraries=/etc/prometheus/console_libraries
      - --web.console.templates=/etc/prometheus/consoles
      - --storage.tsdb.retention.time=200h
      - --web.enable-lifecycle
      - --web.enable-admin-api
    labels:
      - traefik.enable=true
      - traefik.http.routers.prometheus.rule=Host(`prom.msarknet.me`)
      - traefik.http.routers.prometheus.entrypoints=websecure
      - traefik.http.routers.prometheus.tls=true
      - traefik.http.routers.prometheus.middlewares=secure-headers@file,auth@file
      - traefik.http.services.prometheus.loadbalancer.server.port=9090
