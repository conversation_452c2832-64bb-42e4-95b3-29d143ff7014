networks:
  proxy:
    external: true
    name: proxy

services:
  health-fe:
    container_name: health-fe
    build:
      context: ../health-fe
      dockerfile: Dockerfile
      target: development
    restart: unless-stopped
    volumes:
      - ../health-fe:/app
      - ../health-fe/node_modules:/app/node_modules
    labels:
      - traefik.enable=true
      - traefik.http.routers.health-fe.rule=Host(`status.msarknet.me`)
      - traefik.http.routers.health-fe.entrypoints=websecure
      - traefik.http.routers.health-fe.tls=true
      - traefik.http.routers.health-fe.middlewares=spa-headers@file
      - traefik.http.services.health-fe.loadbalancer.server.port=5174
    networks:
      - proxy

  health-be:
    container_name: health-be
    build:
      context: ../health-be
      dockerfile: Dockerfile
      target: development
    restart: unless-stopped
    volumes:
      - ../health-be:/app
      - ../health-be/node_modules:/app/node_modules
      - /var/run/docker.sock:/var/run/docker.sock
    labels:
      - traefik.enable=true
      - traefik.http.routers.health-be.rule=Host(`api-status.msarknet.me`)
      - traefik.http.routers.health-be.entrypoints=websecure
      - traefik.http.routers.health-be.tls=true
      - traefik.http.routers.health-be.middlewares=secure-headers@file,cors-headers@file
      - traefik.http.services.health-be.loadbalancer.server.port=5001
    networks:
      - proxy
