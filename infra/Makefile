# Makefile - Unified project + Docker management for MSArkNet
# Usage: make <target>
# Default goal prints categorized help with emojis :)

# =========================
# 🔧 Variables
# =========================
DOCKER_COMPOSE := docker compose
COMPOSE_INFRA := docker compose -f docker-compose.infrastructure.yml
COMPOSE_CEREBRO := docker compose -f docker-compose.cerebro.yml
COMPOSE_HEALTH := docker compose -f docker-compose.health.yml
COMPOSE_DOCS := docker compose -f docker-compose.docs.yml
PROJECT := msarknet
NETWORK := proxy

# Colors
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
PURPLE := \033[0;35m
CYAN := \033[0;36m
NC := \033[0m

.DEFAULT_GOAL := help

##@ 🐳 Docker Compose Operations
.PHONY: install
install: ## 📦 Full install (first time only)
	@echo -e "$(BLUE)🚀 Installing environment...$(NC)"
	@chmod +x _scripts/setup.sh _scripts/hosts-functions.sh _scripts/generate-certs.sh
	@./_scripts/setup.sh
	@echo -e "$(GREEN)✅ Installation completed!$(NC)"


##@ 🎯 Grouped Services Management
.PHONY: up-all
up-all: up-infra up-cerebro up-health up-docs up-monitoring ## 🚀 Start all services

.PHONY: up-infra
up-infra: network-create ## 🚀 Start infrastructure services
	@echo -e "$(GREEN)🚀 Starting infrastructure...$(NC)"
	@$(COMPOSE_INFRA) up -d
	@$(COMPOSE_INFRA) ps

.PHONY: up-cerebro
up-cerebro: ## 🧠 Start Cerebro project
	@echo -e "$(GREEN)🧠 Starting Cerebro project...$(NC)"
	@$(COMPOSE_CEREBRO) up -d
	@$(COMPOSE_CEREBRO) ps

.PHONY: up-health
up-health: ## ❤️ Start Health project
	@echo -e "$(GREEN)❤️ Starting Health project...$(NC)"
	@$(COMPOSE_HEALTH) up -d
	@$(COMPOSE_HEALTH) ps

.PHONY: up-docs
up-docs: ## 📚 Start Documentation
	@echo -e "$(GREEN)📚 Starting Documentation...$(NC)"
	@$(COMPOSE_DOCS) up -d
	@$(COMPOSE_DOCS) ps

.PHONY: up-monitoring
up-monitoring: ## 📊 Start Monitoring services
	@echo -e "$(GREEN)📊 Starting Monitoring...$(NC)"
	@docker compose -f docker-compose.monitoring.yml up -d
	@docker compose -f docker-compose.monitoring.yml ps

.PHONY: down-all
down-all: down-monitoring down-docs down-health down-cerebro down-infra ## ⏹️ Stop all services

.PHONY: down-infra
down-infra: ## ⏹️ Stop infrastructure
	@$(COMPOSE_INFRA) down

.PHONY: down-cerebro
down-cerebro: ## ⏹️ Stop Cerebro
	@$(COMPOSE_CEREBRO) down

.PHONY: down-health
down-health: ## ⏹️ Stop Health
	@$(COMPOSE_HEALTH) down

.PHONY: down-docs
down-docs: ## ⏹️ Stop Documentation
	@$(COMPOSE_DOCS) down

.PHONY: down-monitoring
down-monitoring: ## ⏹️ Stop Monitoring
	@docker compose -f docker-compose.monitoring.yml down


##@ 🔍 Project-specific logs
logs-infra: ## 📝 Infrastructure logs
	@$(COMPOSE_INFRA) logs -f

logs-cerebro: ## 📝 Cerebro logs
	@$(COMPOSE_CEREBRO) logs -f

logs-health: ## 📝 Health logs
	@$(COMPOSE_HEALTH) logs -f

logs-docs: ## 📝 Documentation logs
	@$(COMPOSE_DOCS) logs -f

logs-monitoring: ## 📝 Monitoring logs
	@docker compose -f docker-compose.monitoring.yml logs -f


##@ 📊 Statistics and Monitoring
.PHONY: stats
stats: ## 📊 Show container resource usage
	@echo -e "$(CYAN)📊 Container resource usage:$(NC)"
	@docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"

.PHONY: stats-live
stats-live: ## 📊 Show live container resource usage
	@echo -e "$(CYAN)📊 Live container resource usage:$(NC)"
	@docker stats
	.PHONY: health


##@ 🎯 Service Management
# 📦 Multi-stack docker compose helpers
# Usa variables COMPOSE_<STACK>, p.ej.:
#   COMPOSE_INFRA   = docker compose -f infra/docker-compose.yml
#   COMPOSE_CEREBRO = docker compose -f cerebro/docker-compose.yml
#   COMPOSE_HEALTH  = docker compose -f health/docker-compose.yml
# Selección por defecto:
STACK ?= INFRA
COMPOSE ?= $(COMPOSE_$(STACK))

# Descubre stacks disponibles a partir de las variables definidas
STACKS := $(patsubst COMPOSE_%,%,$(filter COMPOSE_%,$(.VARIABLES)))

# Guardia: verifica que COMPOSE_$(STACK) exista
define REQUIRE_COMPOSE
	@if [ -z "$(COMPOSE)" ]; then \
		echo "❌ STACK='$(STACK)' no resuelve a COMPOSE_$(STACK)."; \
		echo "   Define una variable como: COMPOSE_$(STACK)=docker compose -f <ruta>"; \
		echo "   Stacks disponibles detectados: $(STACKS)"; \
		exit 1; \
	fi
endef

.PHONY: stacks
stacks: ## 📚 Lista los stacks disponibles (derivados de COMPOSE_*)
	@echo "Stacks disponibles:"
	@for s in $(STACKS); do echo " - $$s"; done

.PHONY: services
services: ## 🧩 Lista servicios del STACK actual (usa STACK=<...>)
	$(REQUIRE_COMPOSE)
	@echo "STACK=$(STACK)"
	@$(COMPOSE) config --services

.PHONY: start/%
start/%: stacks ## ▶️  Start: make start/<servicio> STACK=INFRA
	$(REQUIRE_COMPOSE)
	@$(COMPOSE) up -d $*

.PHONY: stop/%
stop/%: stacks ## ⏹️  Stop: make stop/<servicio> STACK=CEREBRO
	$(REQUIRE_COMPOSE)
	@$(COMPOSE) stop $*

.PHONY: restart/%
restart/%: stacks ## 🔄 Restart: make restart/<servicio> STACK=HEALTH
	$(REQUIRE_COMPOSE)
	@$(COMPOSE) restart $*

.PHONY: logs/%
logs/%: stacks ## 📝 Logs: make logs/<servicio> STACK=DOCS
	$(REQUIRE_COMPOSE)
	@$(COMPOSE) logs -f $*

.PHONY: ps/%
ps/%: stacks ## 📋 ps (solo ese servicio): make ps/<servicio> STACK=MONITORING
	$(REQUIRE_COMPOSE)
	@$(COMPOSE) ps $*

.PHONY: inspect/%
shell/%: stacks ## 🐚 Shell: make shell/<servicio> STACK=INFRA
	$(REQUIRE_COMPOSE)
	@$(COMPOSE) exec $* sh || $(COMPOSE) exec $* bash

inspect/%: stacks ## 🔍 Inspect contenedor del servicio: make inspect/<servicio> STACK=CEREBRO
	$(REQUIRE_COMPOSE)
	@docker inspect $$( $(COMPOSE) ps -q $* )

.PHONY: port/%
port/%: stacks ## 🔌 Puertos mapeados: make port/<servicio> STACK=HEALTH
	$(REQUIRE_COMPOSE)
	@cid=$$($(COMPOSE) ps -q $*); \
	if [ -z "$$cid" ]; then echo "❌ Servicio '$*' no encontrado o no está corriendo"; exit 1; fi; \
	echo "Puertos para $*: "; \
	docker inspect -f '{{range $p,$v := .NetworkSettings.Ports}}{{$p}} -> {{range $v}}{{.HostIp}}:{{.HostPort}} {{end}}{{printf "\n"}}{{end}}' $$cid


##@ 🔍 Inspection and Debugging
.PHONY: inspect-network
inspect-network: ## 🔍 Inspect proxy network
	@echo -e "$(CYAN)🔍 Inspecting $(NETWORK) network...$(NC)"
	@docker network inspect $(NETWORK)

.PHONY: inspect-all
inspect-all: ## 🔍 Inspect all containers
	@echo -e "$(CYAN)🔍 Inspecting all running containers...$(NC)"
	@docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"


##@ 🧹 Docker Cleanup
.PHONY: list-all
list-all: ## 📋 Lista todos los recursos Docker
	@echo "== Containers ==" && docker ps -a || true
	@echo ""
	@echo "== Images ==" && docker images -a || true
	@echo ""
	@echo "== Volumes ==" && docker volume ls || true
	@echo ""
	@echo "== Networks ==" && docker network ls || true
	@echo ""
	@echo "== Contexts ==" && docker context ls || true
	@echo ""
	@echo "Current context:" && docker context show || true
	@echo ""
	@echo "== Builders ==" && docker buildx ls || true

.PHONY: prune-safe
prune-safe: ## 🧹 Limpieza segura (solo no-usados)
	@docker system prune -a --volumes -f
	@docker builder prune -a -f || true
	@docker buildx prune -a -f || true

.PHONY: nuke-docker
nuke-docker: ## ☢️  BORRA TODO (contenedores, imágenes, volúmenes, redes, contexts y builds)
	@bash -c '$(MAKE) _nuke_script'

.PHONY: _nuke_script
_nuke_script:
	@bash -c '\
	CURRENT_CTX=$$(docker context show 2>/dev/null || echo default); \
	echo "Contexto actual: $$CURRENT_CTX"; \
	docker context use default 2>/dev/null || true; \
	IDS=$$(docker ps -aq); [ -n "$$IDS" ] && docker stop $$IDS || true; [ -n "$$IDS" ] && docker rm -f $$IDS || true; \
	IMG=$$(docker images -aq); [ -n "$$IMG" ] && docker rmi -f $$IMG || true; \
	VOL=$$(docker volume ls -q); [ -n "$$VOL" ] && docker volume rm $$VOL || true; \
	NET=$$(docker network ls -q | xargs -r docker network inspect --format "{{.Name}}" | grep -Ev "^(bridge|host|none)$$" || true); \
	[ -n "$$NET" ] && echo "$$NET" | xargs -r docker network rm || true; \
	docker builder prune -a -f || true; docker buildx prune -a -f || true; \
	INACTIVE_BUILDERS=$$(docker buildx ls 2>/dev/null | awk "/inactive/ {print \$$1}"); \
	[ -n "$$INACTIVE_BUILDERS" ] && echo "$$INACTIVE_BUILDERS" | xargs -r -n1 docker buildx rm || true; \
	for ctx in $$(docker context ls --format "{{.Name}}" 2>/dev/null); do \
	  if [ "$$ctx" != "default" ] && [ "$$ctx" != "$$CURRENT_CTX" ]; then docker context rm -f "$$ctx" || true; fi; \
	done; \
	docker system prune -a --volumes -f || true; \
	echo "✅ Docker limpiado a fondo."; \
	'


##@ 🌐 Network Management
.PHONY: network-create
network-create: ## 🌐 Create network if not exists
	@docker network inspect $(NETWORK) >/dev/null 2>&1 || { \
		echo -e "$(BLUE)🌐 Creating $(NETWORK) network...$(NC)"; \
		docker network create $(NETWORK); \
	}
	@echo -e "$(YELLOW)⚠️  $(NETWORK) network ready$(NC)"

.PHONY: network-remove
network-remove: ## 🌐 Remove network
	@echo -e "$(RED)🌐 Removing $(NETWORK) network...$(NC)"
	@docker network rm $(NETWORK) 2>/dev/null || echo -e "$(YELLOW)⚠️  Network doesn't exist$(NC)"

.PHONY: network-ls
network-ls: ## 🌐 List networks
	@docker network ls

.PHONY: network-connect-traefik
network-connect-traefik: ## 🌐 Connect Traefik to network
	@docker network connect $(NETWORK) $(shell $(DOCKER_COMPOSE) ps -q traefik) 2>/dev/null || echo -e "$(YELLOW)⚠️  Already connected$(NC)"


##@ 🔐 Security Operations
.PHONY: htpasswd
htpasswd: ## 🔐 Create htpasswd entry (bcrypt). Usage: make htpasswd USER=admin PASS=admin
	@if [ -z "$(user)" ] || [ -z "$(pass)" ]; then \
		echo "❌ Debes indicar user y pass, por ejemplo:"; \
		echo "   make htpasswd user=admin pass=admin"; \
		exit 1; \
	fi
	@htpasswd -nbB $(user) "$(pass)"

.PHONY: certs
certs: ## 🔒 Generate SSL certificates
	@echo -e "$(YELLOW)🔒 Generating SSL certificates...$(NC)"
	@./_scripts/generate-certs.sh
	@echo -e "$(GREEN)✅ Certificates generated$(NC)"

.PHONY: certs-info
certs-info: ## 📋 View certificate information
	@echo -e "$(CYAN)📋 Certificate information:$(NC)"
	@if [ -f certs/$(CERT_DOMAIN).crt ]; then \
		openssl x509 -in certs/$(CERT_DOMAIN).crt -text -noout | grep -A1 "Subject:\|Not Before\|Not After\|DNS:"; \
	else \
		echo -e "$(RED)❌ Certificado no encontrado. Ejecuta 'make certs'$(NC)"; \
	fi

.PHONY: certs-clean
certs-clean: ## 🧹 Clean generated certificates
	@echo -e "$(YELLOW)🧹 Cleaning certificates...$(NC)"
	@rm -rf certs/*.crt certs/*.key certs/*.conf
	@echo -e "$(GREEN)✅ Certificates cleaned$(NC)"


##@ 🌐 Hosts File Management
# Usage examples:
#   make hosts-check HOSTS="msarknet.me grafana.msarknet.me"
#   make hosts-add   HOSTS="msarknet.me,api.msarknet.me" IP=127.0.0.1 COMMENT="MsArkNet local"
#   make hosts-remove HOSTS="msarknet.me api.msarknet.me"
#   make hosts-add-line LINE="127.0.0.1 myapp.local # custom"

HOSTS_FILE ?= /etc/hosts
IP ?= 127.0.0.1
COMMENT ?= Managed by Makefile

.PHONY: hosts-add hosts-remove hosts-add-line

hosts-add: ## ➕ Add entries for HOSTS at IP (idempotent)
	@set -e; \
	if [ -z "$(HOSTS)" ]; then \
		echo -e "$(YELLOW)⚠️  Provide HOSTS (e.g., HOSTS=\"msarknet.me api.msarknet.me\")$(NC)"; \
		exit 1; \
	fi; \
	for h in $$(echo "$(HOSTS)" | tr ',' ' '); do \
		if grep -E "^[^#]*\\b$$h\\b" "$(HOSTS_FILE)" >/dev/null; then \
			echo -e "$(YELLOW)↪️  Skipping (exists):$(NC) $$h"; \
		else \
			echo -e "$(BLUE)➕ Adding:$(NC) $(IP) $$h  # $(COMMENT)"; \
			echo "$(IP) $$h	# $(COMMENT)" | sudo tee -a "$(HOSTS_FILE)" >/dev/null; \
		fi; \
	done

hosts-add-line: ## ➕ Add a raw LINE to hosts (use LINE="127.0.0.1 foo # comment")
	@set -e; \
	if [ -z "$(LINE)" ]; then \
		echo -e "$(YELLOW)⚠️  Provide LINE (e.g., LINE=\"127.0.0.1 foo.local # comment\")$(NC)"; \
		exit 1; \
	fi; \
	echo -e "$(BLUE)➕ Adding raw line:$(NC) $(LINE)"; \
	echo "$(LINE)" | sudo tee -a "$(HOSTS_FILE)" >/dev/null

hosts-remove:  ## ➖ Remove any lines that contain each host in HOSTS
	@set -e; \
	if [ -z "$(HOSTS)" ]; then \
		echo -e "$(YELLOW)⚠️  Provide HOSTS (e.g., HOSTS=\"msarknet.me api.msarknet.me\")$(NC)"; \
		exit 1; \
	fi; \
	for h in $$(echo "$(HOSTS)" | tr ',' ' '); do \
		echo -e "$(RED)➖ Removing entries containing:$(NC) $$h"; \
		sudo sed -i.bak '/\\b'$$h'\\b/d' "$(HOSTS_FILE)"; \
	done; \
	echo -e "$(GREEN)✅ Hosts entries processed$(NC)"


##@ ⚡ Quick Actions
.PHONY: quick-status status
quick-status status: ## ⚡ Quick status check
	@printf "$(CYAN)⚡ Quick Status:$(NC)\n"
	@printf "$(BLUE)Infrastructure:$(NC)\n"
	@$(COMPOSE_INFRA) ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || true
	@printf "$(BLUE)Cerebro:$(NC)\n"
	@$(COMPOSE_CEREBRO) ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || true
	@printf "$(BLUE)Health:$(NC)\n"
	@$(COMPOSE_HEALTH) ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || true
	@printf "$(BLUE)Documentation:$(NC)\n"
	@$(COMPOSE_DOCS) ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || true
	@printf "$(BLUE)Monitoring:$(NC)\n"
	@docker compose -f docker-compose.monitoring.yml ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || true


##@ 📚 Information
.PHONY: docker-info
docker-info: ## ℹ️  Show Docker system information
	@echo -e "$(CYAN)ℹ️  Docker System Information:$(NC)"
	@echo -e "$(BLUE)Docker version:$(NC)"; docker --version
	@echo -e "$(BLUE)Docker Compose version:$(NC)"; docker compose version
	@echo -e "$(BLUE)Available networks:$(NC)"; docker network ls | grep -E "($(NETWORK)|bridge)"
	@echo -e "$(BLUE)Running containers:$(NC)"; docker ps --format "table {{.Names}}\t{{.Status}}"

.PHONY: container-sizes
container-sizes: ## 📊 Show container sizes
	@echo -e "$(CYAN)📊 Container sizes:$(NC)"
	@docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep -E "(traefik|mariadb|adminer|grafana|prometheus|portainer)" || true

.PHONY: help
help: ## 💡 Show this help
	@awk 'BEGIN {FS = ":.*##"; printf "\n$(CYAN)🐳 MSArkNet Docker Management$(NC)\n\n"} /^[a-zA-Z0-9_.-]+:.*?##/ { printf "  $(GREEN)%-25s$(NC) %s\n", $$1, $$2 } /^##@/ { printf "\n$(YELLOW)%s$(NC)\n", substr($$0, 5) } ' $(MAKEFILE_LIST)
