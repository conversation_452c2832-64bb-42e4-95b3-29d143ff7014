# Project information
site_name: MsArkNet Docs
site_url: https://docs.msarknet.me/
site_description: Documentación completa del stackde  MsArkNet - Infraestructura, desarrollo y guías.
site_author: MsArkNet
copyright: <a href="https://msarknet.me/" target="_blank">MsArkNet web</a>

# Extra project info and template customisation
extra:
  support:
    discord: https://discord.gg/u3Ej2BReNn
    website: https://docs.MsArkNet.me
    issues: https://github.com/MsArkNet/webs/issues
  version:
    provider: mike

# Repository
repo_name: MsArk
repo_url: https://github.com/MsArk
edit_uri: tree/main/sso/mkdocs/docs

theme:
    name: material
    custom_dir: docs/custom_theme
    palette:
        - scheme: default
          primary: teal
          accent: teal
          toggle:
            icon: material/brightness-7
            name: Switch to dark mode
        - scheme: slate
          primary: teal
          accent: teal
          toggle:
            icon: material/brightness-4
            name: Switch to light mode
    favicon: images/favicon.ico
    logo: assets/logo.png
    features:
      - navigation.tabs
      - navigation.tabs.sticky
      - navigation.sections
      - navigation.expand
      - navigation.path
      - navigation.top
      - search.highlight
      - search.share
      - content.code.copy
      - content.code.annotate

nav:
  - Inicio: index.md
  - Infraestructura:
    - infraestructura/index.md
    - Configuración Traefik: infraestructura/traefik-setup.md
    - Certificados SSL: infraestructura/ssl-certificates.md
    - Troubleshooting: infraestructura/troubleshooting.md
  - Desarrollo:
    - desarrollo/index.md
    - Checklist Implementación: desarrollo/checklist-implementacion.md
  - Sprints:
    - sprints/index.md
    - Plan de Sprints: sprints/plan-sprints.md
  - Guías:
    - guias/index.md
    - Instalaciones: guias/instalaciones.md

markdown_extensions:
    - toc:
        permalink: 
        toc_depth: 3
    - codehilite
    - markdown_include.include:
        base_path: docs
    - admonition
    - footnotes
    - def_list
    - abbr
    - pymdownx.arithmatex
    - pymdownx.betterem:
          smart_enable: all
    - pymdownx.keys
    - pymdownx.details
    - pymdownx.emoji
    - pymdownx.magiclink
    - pymdownx.mark
    - pymdownx.smartsymbols
    - pymdownx.superfences
    - pymdownx.tasklist:
          custom_checkbox: true
    - pymdownx.tilde
    - meta
    - smarty

plugins:
    - search
