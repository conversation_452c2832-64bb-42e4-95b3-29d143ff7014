# Certificados web
sudo apt install -y mkcert libnss3-tools  # o brew install mkcert
mkcert -install
mkcert -key-file traefik/dynamic/certs/MsArkNet-key.pem \
       -cert-file traefik/dynamic/certs/MsArkNet-cert.pem \
       MsArkNet.me grafana.MsArkNet.me prom.MsArkNet.me

# Proyecto react vite
PROJECT_NAME=health-be
npm create vite@latest "$PROJECT_NAME" -- --template react-swc-ts

# ICONOS
https://react-icons.github.io/react-icons/
npm install react-icons --save
