# Guías

Esta sección contiene guías prácticas y tutoriales para el desarrollo y configuración del proyecto.

## 📋 Documentos Disponibles

### [Instalaciones](instalaciones.md)
Guía rápida de instalación de herramientas y dependencias necesarias:

- Certificados web con mkcert
- Configuración de proyectos React con Vite
- Instalación de librerías de iconos
- Comandos útiles para desarrollo

## 🛠️ Herramientas Principales

### Certificados SSL
```bash
# Instalar mkcert
sudo apt install -y mkcert libnss3-tools  # Ubuntu/Debian
brew install mkcert                        # macOS

# Configurar CA local
mkcert -install

# Generar certificados para dominios
mkcert -key-file traefik/dynamic/certs/MsArkNet-key.pem \
       -cert-file traefik/dynamic/certs/MsArkNet-cert.pem \
       MsArkNet.me grafana.MsArkNet.me prom.MsArkNet.me
```

### Desarrollo Frontend
```bash
# Crear proyecto React con Vite
PROJECT_NAME=health-be
npm create vite@latest "$PROJECT_NAME" -- --template react-swc-ts

# Instalar iconos
npm install react-icons --save
```

## 🔗 Recursos Útiles

- [React Icons](https://react-icons.github.io/react-icons/) - Librería de iconos para React
- [Vite](https://vitejs.dev/) - Build tool para desarrollo frontend
- [mkcert](https://github.com/FiloSottile/mkcert) - Herramienta para certificados SSL locales

## 📚 Próximas Guías

- Configuración de entorno de desarrollo
- Guía de contribución al proyecto
- Mejores prácticas de desarrollo
- Configuración de IDE y herramientas
