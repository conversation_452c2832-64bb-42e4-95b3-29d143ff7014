# Certificados SSL/TLS

Guía completa para la gestión de certificados SSL/TLS en el entorno de desarrollo MsArkNet.

## 🔒 Visión General

Los certificados SSL/TLS son esenciales para:

- **Desarrollo local seguro** con HTTPS
- **Simulación del entorno de producción**
- **Compatibilidad con navegadores modernos**
- **Testing de funcionalidades que requieren HTTPS**

## 🛠️ Herramientas Utilizadas

### mkcert
Herramienta para generar certificados SSL locales de confianza.

```bash
# Instalación en Ubuntu/Debian
sudo apt install -y mkcert libnss3-tools

# Instalación en macOS
brew install mkcert

# Instalación en Windows
choco install mkcert
```

## ⚡ Configuración Rápida

### 1. Instalar y configurar mkcert

```bash
# Instalar la CA local
mkcert -install

# Verificar instalación
mkcert -CAROOT
```

### 2. Generar certificados para MsArkNet

```bash
# Generar certificados para todos los subdominios
mkcert -key-file traefik/dynamic/certs/MsArkNet-key.pem \
       -cert-file traefik/dynamic/certs/MsArkNet-cert.pem \
       MsArkNet.me \
       "*.MsArkNet.me" \
       grafana.MsArkNet.me \
       prom.MsArkNet.me \
       traefik.MsArkNet.me \
       portainer.MsArkNet.me \
       docs.MsArkNet.me
```

### 3. Configurar Traefik

Los certificados se configuran automáticamente en Traefik a través del archivo `dynamic/tls.yml`:

```yaml
tls:
  certificates:
    - certFile: /etc/traefik/certs/MsArkNet-cert.pem
      keyFile: /etc/traefik/certs/MsArkNet-key.pem
```

## 📁 Estructura de Certificados

```
certs/
├── MsArkNet-cert.pem     # Certificado público
├── MsArkNet-key.pem      # Clave privada
└── MsArkNet.conf         # Configuración (opcional)
```

## 🔧 Configuración Manual

### Generar certificados con OpenSSL

Si prefieres usar OpenSSL directamente:

```bash
# Crear clave privada
openssl genrsa -out MsArkNet-key.pem 2048

# Crear CSR (Certificate Signing Request)
openssl req -new -key MsArkNet-key.pem -out MsArkNet.csr \
  -subj "/C=ES/ST=Madrid/L=Madrid/O=MsArkNet/CN=MsArkNet.me"

# Generar certificado autofirmado
openssl x509 -req -in MsArkNet.csr -signkey MsArkNet-key.pem \
  -out MsArkNet-cert.pem -days 365 \
  -extensions v3_req -extfile <(cat <<EOF
[v3_req]
subjectAltName = @alt_names
[alt_names]
DNS.1 = MsArkNet.me
DNS.2 = *.MsArkNet.me
DNS.3 = grafana.MsArkNet.me
DNS.4 = prom.MsArkNet.me
DNS.5 = traefik.MsArkNet.me
EOF
)
```

## 🌐 Configuración del Navegador

### Chrome/Chromium

1. Ir a `chrome://settings/certificates`
2. Pestaña "Authorities"
3. Importar el certificado CA de mkcert
4. Marcar "Trust this certificate for identifying websites"

### Firefox

1. Ir a `about:preferences#privacy`
2. Sección "Certificates" → "View Certificates"
3. Pestaña "Authorities"
4. Importar el certificado CA de mkcert

### Safari (macOS)

1. Abrir "Keychain Access"
2. Buscar "mkcert"
3. Doble clic en el certificado
4. Expandir "Trust"
5. Cambiar "When using this certificate" a "Always Trust"

## 🔄 Renovación de Certificados

### Automática con mkcert

Los certificados generados con mkcert tienen una validez de 2 años y 3 meses. Para renovar:

```bash
# Regenerar certificados
./generate-certs.sh

# Reiniciar Traefik para cargar nuevos certificados
docker compose restart traefik
```

### Script de renovación automática

```bash
#!/bin/bash
# renew-certs.sh

echo "🔄 Renovando certificados SSL..."

# Backup de certificados actuales
cp certs/MsArkNet-cert.pem certs/MsArkNet-cert.pem.backup
cp certs/MsArkNet-key.pem certs/MsArkNet-key.pem.backup

# Generar nuevos certificados
mkcert -key-file certs/MsArkNet-key.pem \
       -cert-file certs/MsArkNet-cert.pem \
       MsArkNet.me "*.MsArkNet.me"

# Reiniciar Traefik
docker compose restart traefik

echo "✅ Certificados renovados correctamente"
```

## 🔍 Verificación y Testing

### Verificar certificados

```bash
# Verificar información del certificado
openssl x509 -in certs/MsArkNet-cert.pem -text -noout

# Verificar fechas de validez
openssl x509 -in certs/MsArkNet-cert.pem -dates -noout

# Test de conectividad HTTPS
curl -k -I https://MsArkNet.me
```

### Debugging de problemas SSL

```bash
# Test detallado de SSL
openssl s_client -connect MsArkNet.me:443 -servername MsArkNet.me

# Verificar configuración de Traefik
curl -s http://localhost:8080/api/rawdata | jq '.tls'
```

## ⚠️ Consideraciones de Seguridad

### Desarrollo vs Producción

| Aspecto | Desarrollo | Producción |
|---------|------------|------------|
| **Certificados** | Autofirmados (mkcert) | Let's Encrypt / CA comercial |
| **Validez** | 2+ años | 90 días (Let's Encrypt) |
| **Renovación** | Manual | Automática |
| **Confianza** | Solo local | Globalmente confiable |

### Buenas Prácticas

- ✅ **Nunca** commitear claves privadas al repositorio
- ✅ Usar `.gitignore` para excluir archivos de certificados
- ✅ Rotar certificados regularmente
- ✅ Usar certificados diferentes para cada entorno
- ✅ Monitorizar fechas de expiración

## 🔗 Enlaces Útiles

- [mkcert GitHub](https://github.com/FiloSottile/mkcert)
- [Traefik TLS Documentation](https://doc.traefik.io/traefik/https/tls/)
- [Let's Encrypt](https://letsencrypt.org/)
- [OpenSSL Documentation](https://www.openssl.org/docs/)
