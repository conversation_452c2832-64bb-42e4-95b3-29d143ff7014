# MsArkNet - Entorno Docker con Traefik

Un entorno de desarrollo completo con Traefik como proxy reverso, SSL automático y múltiples servicios para desarrollo local.

## 🚀 Características

- **Traefik v3.5.2** como proxy reverso con SSL automático
- **Certificados SSL locales** para todos los subdominios
- **Dashboard de Traefik** para monitorización
- **Múltiples servicios** preconfigurados
- **Escalabilidad** horizontal de servicios
- **Scripts automatizados** para gestión completa

## 📋 Servicios Disponibles

| Servicio                 | URL                           | Descripción                      |
| ------------------------ | ----------------------------- | -------------------------------- |
| **Aplicación Principal** | https://msarknet.me           | Aplicación web principal (Nginx) |
| **API Backend**          | https://msarknet.me/api       | Servicio API (Traefik/whoami)    |
| **Dashboard Traefik**    | https://traefik.msarknet.me   | Panel de control de Traefik      |
| **Grafana Mock**         | https://grafana.msarknet.me   | Simulación de Grafana            |
| **Prometheus Mock**      | https://prom.msarknet.me      | Simulación de Prometheus         |
| **Portainer Mock**       | https://portainer.msarknet.me | Simulación de Portainer          |
| **Documentación**        | https://docs.msarknet.me      | Documentación del proyecto       |

## 🛠️ Prerrequisitos

- Docker >= 20.x
- Docker Compose >= 2.x
- OpenSSL (para generar certificados)
- Bash (para scripts de automatización)

## ⚡ Instalación Rápida

```bash
# 1. Clonar o descargar el proyecto
git clone <tu-repo> MsArkNet
cd MsArkNet

# 2. Ejecutar setup automático
chmod +x setup.sh
./setup.sh

# 3. ¡Listo! Los servicios estarán corriendo
```

El script de setup automáticamente:

- ✅ Verifica dependencias
- ✅ Crea estructura de directorios
- ✅ Genera certificados SSL
- ✅ Configura /etc/hosts
- ✅ Crea red Docker
- ✅ Inicia todos los servicios

## 🔧 Instalación Manual

Si prefieres configurar paso a paso:

### 1. Crear estructura de directorios

```bash
mkdir -p certs dynamic letsencrypt
```

### 2. Generar certificados SSL

```bash
chmod +x generate-certs.sh
./generate-certs.sh
```

### 3. Configurar /etc/hosts

Rellenar el fichero hosts.txt con las líneas deseadas:

```
127.0.0.1 MsArkNet.me
127.0.0.1 grafana.MsArkNet.me
127.0.0.1 prom.MsArkNet.me
127.0.0.1 traefik.MsArkNet.me
127.0.0.1 portainer.MsArkNet.me
127.0.0.1 docs.MsArkNet.me
```

### 4. Crear red Docker y iniciar servicios

```bash
docker network create proxy
docker compose up -d
```

## 📁 Estructura del Proyecto

```
MsArkNet/
├── 📄 docker-compose.cerebro.yml         # Configuración de Cerebro
├── 📄 docker-compose.docs.yml            # Configuración de Documentación
├── 📄 docker-compose.health.yml          # Configuración de Health
├── 📄 docker-compose.infrastructure.yml  # Configuración de Infraestructura
├── 📄 docker-compose.monitoring.yml      # Configuración de Monitorización
├── 📄 Makefile                           # Makefile con comandos Docker avanzados
├── 📁 _scripts                           # Scripts de automatización
│   ├── setup.sh                          # Script de configuración automática
│   ├── hosts-functions.sh                # Funciones para gestionar /etc/hosts
│   └── generate-certs.sh                 # Generador de certificados SSL
│   └── hosts.txt                         # Lista de hosts a configurar
├── 📁 certs/                             # Certificados SSL
│   ├── msarknet.me.crt
│   ├── msarknet.me.key
│   └── msarknet.conf
├── 📁 dynamic/                           # Configuración dinámica de Traefik
│   ├── tls.yml                           # Configuración TLS
│   └── middlewares.yml                   # Middlewares personalizados
│   └── prometheus.yml                    # Configuración de Prometheus
└── 📁 letsencrypt/                       # Datos de Let's Encrypt (producción)
```

## 🚀 Comandos Rápidos

### Gestión de servicios

```bash
# Iniciar todos los servicios
docker compose up -d

# Ver estado de servicios
docker compose ps

# Ver logs en tiempo real
docker compose logs -f

# Parar todos los servicios
docker compose down

# Reiniciar un servicio específico
docker compose restart traefik
```

### Debugging y monitorización

```bash
# Inspeccionar la red proxy
docker network inspect proxy

# Ver configuración de Traefik
curl -s http://localhost:8080/api/rawdata | jq

# Test de conectividad
curl -k -H "Host: MsArkNet.me" https://localhost/
```

## 🔗 Enlaces Útiles

- [Configuración detallada de Traefik](traefik-setup.md)
- [Guía de certificados SSL](ssl-certificates.md)
- [Troubleshooting](troubleshooting.md)

---

**Documentación generada para el entorno de desarrollo local**
**Stack:** React + Node.js + Python + Docker + Traefik + Kubernetes
