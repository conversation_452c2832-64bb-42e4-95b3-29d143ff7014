# Troubleshooting

Guía completa para resolver problemas comunes en el entorno de desarrollo MsArkNet.

## 🔍 Problemas Comunes

### ❌ Error: "This site can't be reached"

**Síntomas:**
- El navegador no puede conectar con `https://MsArkNet.me`
- Timeout de conexión
- DNS_PROBE_FINISHED_NXDOMAIN

**Soluciones:**

1. **Verificar servicios Docker**
   ```bash
   # Ver estado de contenedores
   docker compose ps

   # Si algún servicio está down
   docker compose up -d
   ```

2. **Comprobar archivo /etc/hosts**
   ```bash
   # Verificar entradas
   cat /etc/hosts | grep MsArkNet

   # Debería mostrar:
   # 127.0.0.1 MsArkNet.me
   # 127.0.0.1 grafana.MsArkNet.me
   # 127.0.0.1 prom.MsArkNet.me
   # 127.0.0.1 traefik.MsArkNet.me
   ```

3. **Reiniciar Traefik**
   ```bash
   docker compose restart traefik
   docker compose logs -f traefik
   ```

### ❌ Error de certificado SSL

**Síntomas:**
- "Your connection is not private"
- NET::ERR_CERT_AUTHORITY_INVALID
- SSL_ERROR_BAD_CERT_DOMAIN

**Soluciones:**

1. **Regenerar certificados**
   ```bash
   # Ejecutar script de generación
   chmod +x generate-certs.sh
   ./generate-certs.sh

   # Reiniciar Traefik
   docker compose restart traefik
   ```

2. **Verificar certificados**
   ```bash
   # Comprobar que existen los archivos
   ls -la certs/

   # Verificar contenido del certificado
   openssl x509 -in certs/MsArkNet-cert.pem -text -noout
   ```

3. **Limpiar caché del navegador**
   - Chrome: `Ctrl+Shift+Delete` → Borrar datos de navegación
   - Firefox: `Ctrl+Shift+Delete` → Limpiar historial reciente
   - Safari: Desarrollar → Vaciar cachés

4. **Reinstalar CA de mkcert**
   ```bash
   mkcert -uninstall
   mkcert -install
   ```

### ❌ Servicios no responden

**Síntomas:**
- 502 Bad Gateway
- 503 Service Unavailable
- Timeout en respuestas

**Soluciones:**

1. **Verificar logs de servicios**
   ```bash
   # Ver logs de todos los servicios
   docker compose logs

   # Ver logs de un servicio específico
   docker compose logs -f main-app
   docker compose logs -f api-service
   ```

2. **Comprobar red Docker**
   ```bash
   # Verificar red proxy
   docker network ls
   docker network inspect proxy

   # Recrear red si es necesario
   docker network rm proxy
   docker network create proxy
   ```

3. **Reiniciar completamente**
   ```bash
   # Parar todos los servicios
   docker compose down

   # Limpiar contenedores y volúmenes
   docker compose down -v --remove-orphans

   # Iniciar de nuevo
   docker compose up -d
   ```

### ❌ Puerto ya en uso

**Síntomas:**
- "Port is already allocated"
- "Address already in use"

**Soluciones:**

1. **Identificar proceso que usa el puerto**
   ```bash
   # Ver qué proceso usa el puerto 80/443
   sudo netstat -tulpn | grep :80
   sudo netstat -tulpn | grep :443

   # O usar lsof
   sudo lsof -i :80
   sudo lsof -i :443
   ```

2. **Terminar proceso conflictivo**
   ```bash
   # Matar proceso por PID
   sudo kill -9 <PID>

   # O parar servicio específico
   sudo systemctl stop apache2
   sudo systemctl stop nginx
   ```

### ❌ Problemas de permisos

**Síntomas:**
- "Permission denied"
- "Cannot create directory"
- Errores al escribir archivos

**Soluciones:**

1. **Verificar permisos de directorios**
   ```bash
   # Comprobar permisos
   ls -la certs/
   ls -la dynamic/

   # Corregir permisos si es necesario
   chmod 755 certs/
   chmod 644 certs/*.pem
   ```

2. **Problemas con Docker**
   ```bash
   # Añadir usuario al grupo docker
   sudo usermod -aG docker $USER

   # Reiniciar sesión o ejecutar
   newgrp docker
   ```

## 🛠️ Comandos de Debugging

### Información del sistema

```bash
# Información de Docker
docker version
docker compose version

# Estado de servicios
docker compose ps
docker stats

# Información de red
docker network ls
ip addr show
```

### Logs y monitorización

```bash
# Logs en tiempo real
docker compose logs -f

# Logs de un servicio específico
docker compose logs -f traefik

# Logs con timestamp
docker compose logs -t

# Últimas 100 líneas de logs
docker compose logs --tail=100
```

### Testing de conectividad

```bash
# Test básico de conectividad
curl -I http://localhost
curl -k -I https://localhost

# Test con headers específicos
curl -k -H "Host: MsArkNet.me" https://localhost/

# Test de API
curl -k https://MsArkNet.me/api

# Test de DNS local
nslookup MsArkNet.me
dig MsArkNet.me
```

### Inspección de Traefik

```bash
# API de Traefik (si está habilitada)
curl -s http://localhost:8080/api/rawdata | jq

# Ver configuración de routers
curl -s http://localhost:8080/api/http/routers | jq

# Ver servicios registrados
curl -s http://localhost:8080/api/http/services | jq

# Ver middlewares
curl -s http://localhost:8080/api/http/middlewares | jq
```

## 🔧 Scripts de Diagnóstico

### Script de verificación completa

```bash
#!/bin/bash
# diagnose.sh

echo "🔍 Diagnóstico del entorno MsArkNet"
echo "=================================="

echo "📋 1. Verificando Docker..."
docker --version
docker compose version

echo "📋 2. Estado de contenedores..."
docker compose ps

echo "📋 3. Verificando red proxy..."
docker network inspect proxy > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Red proxy existe"
else
    echo "❌ Red proxy no existe"
fi

echo "📋 4. Verificando /etc/hosts..."
grep -q "MsArkNet.me" /etc/hosts
if [ $? -eq 0 ]; then
    echo "✅ Entradas en /etc/hosts encontradas"
else
    echo "❌ Faltan entradas en /etc/hosts"
fi

echo "📋 5. Verificando certificados..."
if [ -f "certs/MsArkNet-cert.pem" ]; then
    echo "✅ Certificado encontrado"
    openssl x509 -in certs/MsArkNet-cert.pem -dates -noout
else
    echo "❌ Certificado no encontrado"
fi

echo "📋 6. Test de conectividad..."
curl -k -s -o /dev/null -w "%{http_code}" https://localhost/ | grep -q "200\|301\|302"
if [ $? -eq 0 ]; then
    echo "✅ Conectividad HTTPS OK"
else
    echo "❌ Problemas de conectividad HTTPS"
fi

echo "=================================="
echo "🏁 Diagnóstico completado"
```

### Script de limpieza

```bash
#!/bin/bash
# cleanup.sh

echo "🧹 Limpiando entorno MsArkNet..."

# Parar servicios
docker compose down

# Limpiar contenedores huérfanos
docker compose down --remove-orphans

# Limpiar volúmenes no utilizados
docker volume prune -f

# Limpiar imágenes no utilizadas
docker image prune -f

# Limpiar red si existe
docker network rm proxy 2>/dev/null || true

echo "✅ Limpieza completada"
```

## 📞 Obtener Ayuda

### Información para reportar problemas

Cuando reportes un problema, incluye:

1. **Versión del sistema**
   ```bash
   uname -a
   docker --version
   docker compose version
   ```

2. **Estado de servicios**
   ```bash
   docker compose ps
   docker compose logs --tail=50
   ```

3. **Configuración de red**
   ```bash
   docker network ls
   ip addr show
   cat /etc/hosts | grep MsArkNet
   ```

4. **Logs específicos del error**
   ```bash
   docker compose logs traefik
   ```

### Recursos adicionales

- [Documentación de Docker](https://docs.docker.com/)
- [Documentación de Traefik](https://doc.traefik.io/traefik/)
- [Issues del proyecto](https://github.com/MsArkNet/webs/issues)
- [Discord de la comunidad](https://discord.gg/u3Ej2BReNn)
