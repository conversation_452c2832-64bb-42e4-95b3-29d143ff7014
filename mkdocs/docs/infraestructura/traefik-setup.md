# 🚀 Configuración de Traefik

Traefik está configurado para enrutar automáticamente el tráfico HTTPS usando certificados locales.

!!! info "💡 Tip"
    Todos los servicios están disponibles a través de HTTPS en el dominio `*.MsArkNet.me`

## Servicios Disponibles

| Servicio | URL | Descripción | Estado |
|----------|-----|-------------|--------|
| Aplicación Principal | `https://MsArkNet.me` | Frontend principal | ✅ Activo |
| Dashboard Traefik | `https://traefik.MsArkNet.me` | Panel de control | ✅ Activo |
| Grafana | `https://grafana.MsArkNet.me` | Métricas y dashboards | ✅ Activo |
| Prometheus | `https://prom.MsArkNet.me` | Monitorización | ✅ Activo |

## 🔧 API Endpoints

Documentación de los endpoints disponibles para desarrollo.

### Endpoints Principales

#### GET `/api`
Endpoint principal de la API - devuelve información del servicio

#### GET `/api/health`
Health check del servicio

#### POST `/api/data`
Endpoint para recibir datos (simulado)

### Ejemplo de respuesta

```json
{
  "service": "API Backend Service",
  "version": "1.0.0",
  "timestamp": "2025-09-12T10:30:00Z",
  "environment": "development",
  "ssl": true,
  "cors_enabled": true
}
```

## 🐳 Configuración Docker

Comandos útiles para gestionar el entorno de desarrollo.

### Comandos principales

```bash
# Iniciar todos los servicios
docker compose up -d

# Ver logs de Traefik
docker compose logs -f traefik

# Reiniciar un servicio específico
docker compose restart main-app

# Escalar un servicio
docker compose up -d --scale api-service=3

# Parar todos los servicios
docker compose down
```

### Red Docker

| Configuración | Valor |
|---------------|-------|
| Nombre de red | `proxy` |
| Driver | bridge |
| Subnet | **********/16 |

## 🔒 Certificados SSL/TLS

Configuración de certificados para desarrollo local seguro.

!!! warning "⚠️ Importante"
    Los certificados son autofirmados para desarrollo local. En producción usar Let's Encrypt.

### Generar nuevos certificados

```bash
# Hacer ejecutable el script
chmod +x generate-certs.sh

# Ejecutar generación de certificados
./generate-certs.sh
```

### Configuración de navegador

1. Accede a cualquier servicio HTTPS
2. Haz clic en "Avanzado" cuando aparezca la advertencia
3. Selecciona "Continuar a MsArkNet.me (no es seguro)"
4. El certificado se recordará para futuras visitas

## 🔍 Troubleshooting

### Problemas comunes

#### ❌ Error: "This site can't be reached"

- Verificar que los servicios están ejecutándose: `docker compose ps`
- Comprobar el archivo `/etc/hosts`
- Reiniciar Traefik: `docker compose restart traefik`

#### ❌ Error de certificado SSL

- Regenerar certificados: `./generate-certs.sh`
- Limpiar caché del navegador
- Verificar que los archivos están en `./certs/`

#### ❌ Servicios no responden

- Verificar logs: `docker compose logs [servicio]`
- Comprobar red Docker: `docker network ls`
- Reiniciar completamente: `docker compose down && docker compose up -d`

### Comandos útiles para debugging

```bash
# Ver estado de todos los contenedores
docker compose ps

# Inspeccionar la red proxy
docker network inspect proxy

# Ver configuración de Traefik
curl -s http://localhost:8080/api/rawdata | jq

# Test de conectividad
curl -k -H "Host: MsArkNet.me" https://localhost/
```

## 📝 Orden recomendado en docker-compose.yml

```yaml
# Orden recomendado dentro de un servicio:
container_name
image / build
command / entrypoint
restart
environment / env_file
ports
volumes
networks
depends_on
healthcheck
labels
# Otros (ulimits, secrets, etc.)
```

---

**Stack:** React + Node.js + Python + Docker + Traefik + Kubernetes
