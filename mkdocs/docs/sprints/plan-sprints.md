# Plan de Sprints — SSO "Cerebro" (FE + BE en paralelo)

> Objetivo: entregar un **IdP OIDC** (Cerebro) + **panel frontend** (cerebro‑fe) + **SDK/middleware** para -be clientes (abogados‑be, bitcoin‑be) con **Auth Code + PKCE**, **SSO + back‑channel logout**, **RBAC/consent**, **observabilidad**, **rotación de claves** y **Swagger** para pruebas.

---

## Estructura de repos (sugerida)

* **apps/cerebro-be** (NestJS/Express + Prisma) — Proveedor OIDC.
* **apps/cerebro-fe** (React + Vite + TS) — Panel de sesiones, dispositivos, consent, admin RBAC.
* **apps/abogados-be** y **apps/bitcoin-be** — Resource servers de ejemplo (Swagger + middleware JWT).
* **packages/auth-mw** — Middleware/SDK validación JWT + cache JWKS (Node/Express/Nest).
* **infra/** — Docker Compose (MariaDB, Redis, Traefik + Let's Encrypt, OTEL Collector, Prom/Grafana/Loki), seeders, migraciones.

---

## Sprint 0 — Fundaciones & Infra

**Objetivo:** Entorno reproducible y baseline de calidad.

**Entregables**

* ~~Docker Compose con **MariaDB**, **Redis**, **Traefik** (TLS Let’s Encrypt), **OTel Collector**, **Prometheus**, **Grafana**, **Loki**.~~
* Repos iniciales + CI (lint/test/build) + quality gates.
* **Prisma** conectado a MariaDB, migración inicial + **seed oauth\_clients** (bitcoin, abogados, cerebro‑fe).
* Estructura de **.env.example** (secretos por servicio) + guía de secretos.
* Swagger básico vivo en cada -be (hello + /health).

**Tareas**

* Scaffold repos + monorepo (pnpm/lerna/turbo) o multi‑repo (a elegir).
* Traefik reglas + certificados LE wildcard.
* Prisma schema (users, sessions, oauth\_clients, oauth\_codes, tokens, jwk\_keys, consents, roles, permissions, audit\_logs).
* Seed inicial de clientes con redirect\_uris y backchannel\_logout\_uri.
* Telemetry bootstrap (OTel SDK), structured logging (pino) y correlation-id.

**DoD**

* `docker compose up` levanta todo con TLS.
* `prisma migrate deploy` + `prisma db seed` OK.
* `/health` y `/metrics` accesibles por cada servicio.

---

## Sprint 1 — OIDC Discovery + JWKS + Authorization Endpoint (PKCE)

**Objetivo:** Implementar núcleo OIDC publicable.

**Entregables**

* `/.well-known/openid-configuration` completo.
* `/jwks.json` con **kid** y storage en DB (tabla `jwk_keys`).
* `/authorize` con **Authorization Code + PKCE** (S256), consent mínimo stub.
* UI cerebro‑fe: **Login**, **Selector de cliente**, **pantalla de Consent (stub)**.

**Tareas**

* Generación/rotación manual de par de claves (RSA/ECDSA) y persistencia.
* Validaciones PKCE (code\_verifier, code\_challenge).
* Persistencia de `authorization_code` (tabla `oauth_codes`).
* UI: formularios, estado, redirecciones con `state` y `nonce`.
* Tests integración: flujos `/authorize` happy path + errores (missing redirect\_uri, invalid\_client, invalid\_scope, pkce\_invalid).

**DoD**

* OpenID Provider Metadata válida en OIDC playground.
* Se emite `code` y redirige correctamente al `redirect_uri` del cliente.

---

## Sprint 2 — Token Endpoint + Emisión JWT (ID/Access) + Middleware JWT

**Objetivo:** Completar Auth Code Flow end‑to‑end y consumo por APIs.

**Entregables**

* `/token` con `authorization_code` + **PKCE** y **refresh\_token** (rotación activada).
* Emisión **ID Token** y **Access Token JWT** con `kid`, `aud` por cliente, `scope`/permissions y `exp`.
* **packages/auth-mw**: middleware de validación JWT (firma, `aud`, `exp`, `nbf`) + **cache JWKS** con TTL + fallback.
* Swagger en abogados‑be/bitcoin‑be con **OAuth2 Auth Code** para probar.

**Tareas**

* Claims: `iss`, `sub`, `aud`, `azp`, `iat`, `exp`, `auth_time`, `amr`, `nonce` (si aplica), `scope`, `roles` (si ya disponibles), `sid`.
* Refresh token rotation + revocación en DB (tabla `tokens`).
* UI: flujo completo login → consent → redirect al cliente demo.
* Tests: firma JWS, invalid aud, token expirado, JWKS cache miss/hit.

**DoD**

* Cliente demo llama a abogados‑be/bitcoin‑be con `Authorization: Bearer` y pasa middleware.

---

## Sprint 3 — SSO Logout + Gestión de Sesiones (FE)

**Objetivo:** Cerrar sesiones globalmente y visibilidad al usuario.

**Entregables**

* **Front‑channel y back‑channel logout**: notificación a `backchannel_logout_uri` y limpieza de `sid`.
* Panel **cerebro‑fe**: sesiones activas, dispositivos, cerrar sesión por dispositivo, revocar refresh tokens.
* Endpoint `/revocation` (RFC 7009) y `/introspect` (opcional para debug).

**Tareas**

* Modelo de **session store en Redis** (sid ↔ user ↔ clients ↔ tokens).
* Auditoría de eventos: login, token\_issued, refresh\_rotated, logout, revoke.
* UI: tabla de sesiones, device fingerprint básico, acciones.
* Tests e2e (Playwright): login multi‑cliente, logout global, back‑channel recibido por clientes demo.

**DoD**

* Al hacer logout global, clientes reciben back‑channel y niegan tokens siguientes.

---

## Sprint 4 — RBAC/Permissions + Consent real por cliente

**Objetivo:** Control fino de acceso y consentimiento configurable.

**Entregables**

* Modelo **RBAC**: `roles`, `permissions`, `role_permissions`, `user_roles` por **cliente**.
* **Consent UI**: scopes/grants por cliente; recordar decisión.
* Mapeo de **roles/permissions → claims** en Access/ID token.
* Panel admin en **cerebro‑fe**: asignar roles a usuarios, ver permisos por cliente.

**Tareas**

* Migraciones Prisma + seed roles/permisos base.
* Política de scopes (`openid email profile offline_access …` + custom `abogados:read`, `bitcoin:tx:*`).
* Filtros por `aud` y `scope` en middleware.
* Tests: cambio de rol refleja claims; denegación por falta de permiso.

**DoD**

* Un usuario sin permiso no puede acceder a endpoint protegido aunque tenga token válido.

---

## Sprint 5 — Observabilidad & Seguridad Operacional

**Objetivo:** Visibilidad total + límites de abuso.

**Entregables**

* **Audit logs** consultables en Grafana/Loki.
* **Métricas** (Prometheus): tasas de login, errores por tipo, latencias, 95/99p; cardinalidad controlada.
* **Traces** OTel end‑to‑end (idp ↔ clientes) con baggage `user_id` (anonimizado) y `request_id`.
* **Rate limiting** por IP/cliente/usuario para `/authorize`, `/token`, `/jwks`, `/introspect`, `/revocation`.

**Tareas**

* Exporters OTel → Collector → Prom/Grafana.
* Dashboards: salud OIDC, picos de error, top clientes.
* Límites por Redis (token bucket) + respuestas RFC (429 con `Retry-After`).
* Hardening: headers de seguridad, CSP en FE, cookie flags, SameSite.

**DoD**

* Tableros listos + alertas básicas (p99 > umbral, 5xx burst, fallo LE renew).

---

## Sprint 6 — Rotación de Claves + KMS/HSM (nivel pro) + Docs

**Objetivo:** Seguridad criptográfica y operativa a largo plazo.

**Entregables**

* Política de **rotación de claves** (programada) y retiro seguro de claves antiguas.
* Integración opcional con **KMS/HSM** (abstracción de firma JWS).
* **Runbooks** y **Swagger** completo (con ejemplos por flujo) + guía de integración para clientes.

**Tareas**

* Tabla `jwk_keys`: estados (active, retired), `kid` inmutable, ventana de solape JWKS.
* Job de rotación (cron) + pruebas de rollover (old kid aún en JWKS).
* Abstracción de firma para usar KMS/HSM o key local.
* Documentación: flujos, secuencias, errores, mapping claims, ejemplos cURL/Postman.

**DoD**

* Rollover sin downtime: tokens nuevos con `kid` nuevo, validación legacy operativa.

---

## Detalle funcional por checklist (trazabilidad)

* **Infra**: MariaDB, Redis, dominios/TLS LE, secretos .env → *Sprint 0*.
* **DB + Prisma + seed oauth\_clients** → *Sprints 0–1*.
* **OIDC Discovery + JWKS** → *Sprint 1*.
* **Auth Code + PKCE + tokens/rotation/revocation** → *Sprints 1–3*.
* **JWT con kid/aud/scope** → *Sprint 2*.
* **Middleware JWT + cache JWKS** → *Sprint 2*.
* **Logout SSO + back‑channel** → *Sprint 3*.
* **Gestión sesiones (FE)** → *Sprint 3*.
* **RBAC/permissions + consent** → *Sprint 4*.
* **Observabilidad (audit, métricas, traces) + rate limiting** → *Sprint 5*.
* **Rotación de claves (KMS/HSM)** → *Sprint 6*.
* **Swagger UI** → *Sprints 0–6 (in crescendo)*.

---

## Historias por sprint (resumen)

**S0**

* Como dev, quiero levantar todo con `docker compose` y TLS.
* Como dev, quiero migrar y seedear DB en un comando.

**S1**

* Como cliente, quiero descubrir capacidades OIDC (`/.well-known`).
* Como usuario, quiero iniciar sesión y obtener un `code` con PKCE.

**S2**

* Como cliente, quiero canjear `code` por tokens (ID/Access/Refresh).
* Como API, quiero validar JWT con JWKS cacheado.

**S3**

* Como usuario, quiero ver/cerrar mis sesiones y dispositivos.
* Como cliente, quiero recibir back‑channel logout y anular `sid`.

**S4**

* Como admin, quiero asignar roles/permissions por cliente.
* Como usuario, quiero aceptar/rechazar scopes por cliente.

**S5**

* Como SRE, quiero métricas/traces/logs con alertas.
* Como plataforma, quiero limitar abusos con rate limiting.

**S6**

* Como seguridad, quiero rotar claves sin impacto.
* Como integrador, quiero docs/Swagger y ejemplos listos.

---

## Backlog técnico (crea issues)

* Plantillas de **.env.example** (todos los servicios) + `scripts/check-env.ts`.
* Comandos NPM: `dev`, `build`, `start`, `migrate`, `seed`, `lint`, `test`, `e2e`.
* **SDK auth-mw** exportando: expressMiddleware(), nestGuard(), jwksClient(cache), verifyJwt(aud,scope), requireScope().
* **Playwright** e2e: login, consent, token, refresh‑rotation, logout back‑channel.
* Dashboards Grafana (OIDC, Redis, DB).
* Scripts de **data retention** (tokens y logs antiguos).

---

## Variables de entorno (borrador .env)

* `CEREBRO_ISSUER=https://auth.tu-dominio.com`
* `CEREBRO_JWKS_ALG=RS256` (o ES256)
* `DATABASE_URL=mariadb://user:pass@maria:3306/cerebro`
* `REDIS_URL=redis://redis:6379/0`
* `COOKIE_SECURE=true` `COOKIE_SAMESITE=Lax`
* `TRUST_PROXY=1`
* `OTEL_EXPORTER_OTLP_ENDPOINT=http://otel-collector:4317`
* `RATE_LIMIT_AUTHZ=10/m` `RATE_LIMIT_TOKEN=20/m`

---

## Criterios de calidad (globales)

* **Seguridad**: PKCE S256 obligatorio, SameSite+Secure, CSRF en FE, scopes mínimos, refresh rotation, back‑channel implementado, RBAC por cliente.
* **Confiabilidad**: pruebas unitarias + integración + e2e; rate limiting; circuit breakers para JWKS.
* **Observabilidad**: logs estructurados, métricas y traces con correlación.
* **DX**: Swagger vivo, ejemplos cURL, scripts de arranque y seeds.
* **Docs**: README por servicio + runbooks.

---

## Próximos pasos

1. Confirmar stack exacto (NestJS vs Express, monorepo vs multi‑repo).
2. Crear repos + `infra/` (Sprint 0) y abrir issues del Sprint 0.
3. Conectar tu **base de React Vite** a `cerebro-idp` (login/consent) desde S1.
