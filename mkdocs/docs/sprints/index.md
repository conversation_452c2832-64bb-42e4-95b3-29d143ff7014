# Plan de Sprints

Esta sección contiene la planificación y seguimiento de los sprints del proyecto SSO "Cerebro".

## 🎯 Objetivo General

Entregar un **IdP OIDC** (Cerebro) + **panel frontend** (cerebro‑fe) + **SDK/middleware** para -be clientes con:

- **Auth Code + PKCE**
- **SSO + back‑channel logout**
- **RBAC/consent**
- **Observabilidad**
- **Rotación de claves**
- **Swagger** para pruebas

## 📋 Documentos Disponibles

### [Plan Detallado de Sprints](plan-sprints.md)

Planificación completa dividida en sprints con objetivos específicos:

- **Sprint 0**: Fundaciones & Infraestructura
- **Sprint 1**: Core OIDC + Auth Code/PKCE
- **Sprint 2**: JWT + JWKS + Middleware
- **Sprint 3**: SSO + Back-channel Logout
- **Sprint 4**: RBAC + Consent + Panel FE
- **Sprint 5**: Observabilidad + Rotación de Claves

## 🏗️ Estructura de Repositorios

```
apps/
├── cerebro-be/           # NestJS/Express + Prisma - Proveedor OIDC
├── cerebro-fe/           # React + Vite + TS - Panel de gestión
├── abogados-be/          # Resource server de ejemplo
└── bitcoin-be/           # Resource server de ejemplo

packages/
└── auth-mw/              # Middleware/SDK validación JWT

infra/
└── docker-compose/       # MariaDB, Redis, Traefik, OTEL, etc.
```

## 📊 Estado Actual

- ✅ Infraestructura base con Docker y Traefik
- 🔄 Configuración de certificados SSL
- ⏳ Implementación del IdP OIDC
- ⏳ Desarrollo del panel frontend

## 🔗 Enlaces Relacionados

- [Checklist de Implementación](../desarrollo/checklist-implementacion.md)
- [Configuración de Infraestructura](../infraestructura/index.md)
- [Guías de Instalación](../guias/instalaciones.md)
