# MsArkNet SSO - Documentación

Bienvenido a la documentación completa del sistema SSO (Single Sign-On) de MsArkNet. Esta documentación está organizada por proyectos y contiene toda la información necesaria para desarrollar, configurar y mantener el sistema.

## 🎯 ¿Qué es MsArkNet SSO?

MsArkNet SSO es un sistema de autenticación centralizada basado en **OpenID Connect (OIDC)** que permite a los usuarios acceder a múltiples aplicaciones con una sola cuenta. El sistema está diseñado para ser:

- **Seguro**: Implementa Authorization Code + PKCE
- **Escalable**: Arquitectura basada en microservicios
- **Observable**: Métricas, logs y trazas completas
- **Moderno**: Stack tecnológico actualizado

## 📚 Estructura de la Documentación

### 🏗️ [Infraestructura](infraestructura/index.md)
Configuración del entorno de desarrollo con Docker, <PERSON><PERSON><PERSON><PERSON>, certificados SSL y servicios de monitorización.

- Configuración de Traefik como proxy reverso
- Gestión de certificados SSL/TLS
- Servicios de desarrollo (Grafana, Prometheus)
- Troubleshooting y debugging

### 💻 [Desarrollo](desarrollo/index.md)
Guías y documentación técnica para el desarrollo del sistema SSO.

- Checklist de implementación paso a paso
- Arquitectura del sistema
- Flujos de autenticación
- Tecnologías utilizadas

### 📅 [Sprints](sprints/index.md)
Planificación y seguimiento del desarrollo organizado en sprints.

- Plan detallado de sprints
- Objetivos y entregables
- Estado actual del proyecto
- Estructura de repositorios

### 📖 [Guías](guias/index.md)
Tutoriales prácticos y guías de instalación.

- Instalación de herramientas
- Configuración de entorno
- Comandos útiles
- Recursos adicionales

## 🚀 Inicio Rápido

1. **Configurar infraestructura**: Sigue la [guía de infraestructura](infraestructura/index.md)
2. **Revisar el checklist**: Consulta el [checklist de implementación](desarrollo/checklist-implementacion.md)
3. **Planificar desarrollo**: Revisa el [plan de sprints](sprints/plan-sprints.md)
4. **Instalar herramientas**: Sigue las [guías de instalación](guias/instalaciones.md)

## 🛠️ Stack Tecnológico

- **Backend**: NestJS/Express + Prisma
- **Frontend**: React + Vite + TypeScript
- **Base de datos**: MariaDB
- **Cache**: Redis
- **Proxy**: Traefik
- **Contenedores**: Docker + Docker Compose
- **Observabilidad**: OpenTelemetry + Prometheus + Grafana

## 🔗 Enlaces Útiles

- [Repositorio del proyecto](https://github.com/MsArkNet/webs)
<!-- - [Discord de la comunidad](https://discord.gg/u3Ej2BReNn) -->
- [Sitio web oficial](https://msarknet.me)
