# Desarrollo

Esta sección contiene documentación relacionada con el desarrollo del proyecto MsArkNet SSO.

## 📋 Documentos Disponibles

### [Checklist de Implementación](checklist-implementacion.md)
Lista de tareas paso a paso para implementar el sistema SSO completo, incluyendo:

- Infraestructura (MariaDB, Redis, dominios y TLS)
- Esquema de base de datos y migraciones
- Implementación OIDC Discovery + JWKS
- Flujo Authorization Code + PKCE
- Gestión de JWT y tokens
- Middleware de validación
- Sistema de logout SSO
- RBAC y permisos
- Observabilidad y métricas

### Arquitectura del Sistema

El sistema SSO está diseñado con los siguientes componentes principales:

- **Cerebro IdP**: Proveedor de identidad OIDC
- **Cerebro FE**: Panel frontend para gestión de sesiones
- **Resource Servers**: Servicios backend (abogados-be, bitcoin-be)
- **Auth Middleware**: SDK para validación JWT

### Tecnologías Utilizadas

- **Backend**: NestJS/Express + Prisma
- **Frontend**: React + Vite + TypeScript
- **Base de datos**: MariaDB
- **Cache**: Redis
- **Proxy**: Traefik
- **Contenedores**: Docker + Docker Compose
- **Observabilidad**: OpenTelemetry + Prometheus + Grafana

### Flujo de Autenticación

1. **Authorization Code + PKCE**: Flujo seguro para SPAs
2. **JWT Tokens**: Con kid y aud por cliente
3. **Scope/Permissions**: Sistema granular de permisos
4. **Session Management**: Gestión centralizada de sesiones
5. **Back-channel Logout**: Logout distribuido

### Próximos Pasos

Consulta el [Plan de Sprints](../sprints/plan-sprints.md) para ver la hoja de ruta detallada del desarrollo.
