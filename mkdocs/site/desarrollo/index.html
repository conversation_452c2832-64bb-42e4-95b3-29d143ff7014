
<!doctype html>
<html lang="en" class="no-js">
  <head>

      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">

        <meta name="description" content="Documentación completa del sistema SSO MsArkNet - Infraestructura, desarrollo y guías.">


        <meta name="author" content="MsArkNet">


        <link rel="canonical" href="https://docs.MsArkNet.me/desarrollo/">


        <link rel="prev" href="../infraestructura/troubleshooting/">


        <link rel="next" href="checklist-implementacion/">


      <link rel="icon" href="../images/favicon.ico">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.20">



        <title>Desarrollo - MsArkNet SSO Documentation</title>



      <link rel="stylesheet" href="../assets/stylesheets/main.e53b48f4.min.css">


        <link rel="stylesheet" href="../assets/stylesheets/palette.06af60db.min.css">












        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>



    <script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>

  <!--Empty the footer analytics bloc as we need it the headers -->



  <link rel="stylesheet" href="../assets/stylesheets/nethereum_custom.css">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.1/css/all.css" integrity="sha384-gfdkjb5BdAXd+lj+gudLWI+BXq4IuLW5IT+brZEZsLFm++aCMlF1V92rMkPaX4PP" crossorigin="anonymous">
  <script
      src="https://code.jquery.com/jquery-1.12.4.min.js"
      integrity="sha256-ZosEbRLbNQzLpnKIkEdrPv7lOy9C27hHQ+Xp8a4MxAQ="
      crossorigin="anonymous"></script>
  <script async src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-MML-AM_CHTML"></script>


  </head>









    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="teal" data-md-color-accent="teal">


    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">


        <a href="#desarrollo" class="md-skip">
          Skip to content
        </a>

    </div>
    <div data-md-component="announce">

    </div>

      <div data-md-color-scheme="default" data-md-component="outdated" hidden>

      </div>






<header class="md-header md-header--shadow md-header--lifted" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href=".." title="MsArkNet SSO Documentation" class="md-header__button md-logo" aria-label="MsArkNet SSO Documentation" data-md-component="logo">

  <img src="../assets/logo.png" alt="logo">

    </a>
    <label class="md-header__button md-icon" for="__drawer">

      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            MsArkNet SSO Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">

              Desarrollo

          </span>
        </div>
      </div>
    </div>


        <form class="md-header__option" data-md-component="palette">




    <input class="md-option" data-md-color-media="" data-md-color-scheme="default" data-md-color-primary="teal" data-md-color-accent="teal"  aria-label="Switch to dark mode"  type="radio" name="__palette" id="__palette_0">

      <label class="md-header__button md-icon" title="Switch to dark mode" for="__palette_1" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>





    <input class="md-option" data-md-color-media="" data-md-color-scheme="slate" data-md-color-primary="teal" data-md-color-accent="teal"  aria-label="Switch to light mode"  type="radio" name="__palette" id="__palette_1">

      <label class="md-header__button md-icon" title="Switch to light mode" for="__palette_0" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>


</form>



      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>





        <label class="md-header__button md-icon" for="__search">

          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">

        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>

        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">

          <a href="javascript:void(0)" class="md-search__icon md-icon" title="Share" aria-label="Share" data-clipboard data-clipboard-text="" data-md-component="search-share" tabindex="-1">

            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.15c-.05.21-.08.43-.08.66 0 1.61 1.31 2.91 2.92 2.91s2.92-1.3 2.92-2.91A2.92 2.92 0 0 0 18 16.08"/></svg>
          </a>

        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">

          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>

    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>



      <div class="md-header__source">
        <!--
  Copyright (c) 2016-2019 Martin Donath <<EMAIL>>

  Permission is hereby granted, free of charge, to any person obtaining a copy
  of this software and associated documentation files (the "Software"), to
  deal in the Software without restriction, including without limitation the
  rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
  sell copies of the Software, and to permit persons to whom the Software is
  furnished to do so, subject to the following conditions:

  The above copyright notice and this permission notice shall be included in
  all copies or substantial portions of the Software.

  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL THE
  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
  IN THE SOFTWARE.
-->



<!--
  Check whether the repository is hosted on one of the supported code hosting
  platforms (GitHub, GitLab or Bitbucket) to show icon.
-->





<!-- Repository containing source -->
<a href="https://github.com/MsArkNet/webs" title="source.link.title"
    class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg viewBox="0 0 24 24" width="24" height="24">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>

  <div class="md-source__repository">
    MsArkNet/webs
  </div>
</a>
      </div>

  </nav>



<nav class="md-tabs" aria-label="Tabs" data-md-component="tabs">
  <div class="md-grid">
    <ul class="md-tabs__list">






    <li class="md-tabs__item">
      <a href=".." class="md-tabs__link">





  Inicio

      </a>
    </li>










      <li class="md-tabs__item">
        <a href="../infraestructura/" class="md-tabs__link">



  Infraestructura

        </a>
      </li>













      <li class="md-tabs__item md-tabs__item--active">
        <a href="./" class="md-tabs__link">



  Desarrollo

        </a>
      </li>











      <li class="md-tabs__item">
        <a href="../sprints/" class="md-tabs__link">



  Sprints

        </a>
      </li>











      <li class="md-tabs__item">
        <a href="../guias/" class="md-tabs__link">



  Guías

        </a>
      </li>




    </ul>
  </div>
</nav>


</header>

    <div class="md-container" data-md-component="container">




      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">



              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">






<nav class="md-nav md-nav--primary md-nav--lifted" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href=".." title="MsArkNet SSO Documentation" class="md-nav__button md-logo" aria-label="MsArkNet SSO Documentation" data-md-component="logo">

  <img src="../assets/logo.png" alt="logo">

    </a>
    MsArkNet SSO Documentation
  </label>

    <div class="md-nav__source">
      <!--
  Copyright (c) 2016-2019 Martin Donath <<EMAIL>>

  Permission is hereby granted, free of charge, to any person obtaining a copy
  of this software and associated documentation files (the "Software"), to
  deal in the Software without restriction, including without limitation the
  rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
  sell copies of the Software, and to permit persons to whom the Software is
  furnished to do so, subject to the following conditions:

  The above copyright notice and this permission notice shall be included in
  all copies or substantial portions of the Software.

  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL THE
  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
  IN THE SOFTWARE.
-->



<!--
  Check whether the repository is hosted on one of the supported code hosting
  platforms (GitHub, GitLab or Bitbucket) to show icon.
-->





<!-- Repository containing source -->
<a href="https://github.com/MsArkNet/webs" title="source.link.title"
    class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg viewBox="0 0 24 24" width="24" height="24">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>

  <div class="md-source__repository">
    MsArkNet/webs
  </div>
</a>
    </div>

  <ul class="md-nav__list" data-md-scrollfix>







    <li class="md-nav__item">
      <a href=".." class="md-nav__link">



  <span class="md-ellipsis">
    Inicio

  </span>


      </a>
    </li>



















    <li class="md-nav__item md-nav__item--nested">





        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_2" >


          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="0">



  <span class="md-ellipsis">
    Infraestructura

  </span>


            <span class="md-nav__icon md-icon"></span>
          </label>

        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            Infraestructura
          </label>
          <ul class="md-nav__list" data-md-scrollfix>







    <li class="md-nav__item">
      <a href="../infraestructura/" class="md-nav__link">



  <span class="md-ellipsis">
    MsArkNet - Entorno Docker con Traefik

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="../infraestructura/traefik-setup/" class="md-nav__link">



  <span class="md-ellipsis">
    Configuración Traefik

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="../infraestructura/ssl-certificates/" class="md-nav__link">



  <span class="md-ellipsis">
    Certificados SSL

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="../infraestructura/troubleshooting/" class="md-nav__link">



  <span class="md-ellipsis">
    Troubleshooting

  </span>


      </a>
    </li>




          </ul>
        </nav>

    </li>
























    <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">



        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" checked>


          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="">



  <span class="md-ellipsis">
    Desarrollo

  </span>


            <span class="md-nav__icon md-icon"></span>
          </label>

        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Desarrollo
          </label>
          <ul class="md-nav__list" data-md-scrollfix>









    <li class="md-nav__item md-nav__item--active">

      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">





        <label class="md-nav__link md-nav__link--active" for="__toc">



  <span class="md-ellipsis">
    Desarrollo

  </span>


          <span class="md-nav__icon md-icon"></span>
        </label>

      <a href="./" class="md-nav__link md-nav__link--active">



  <span class="md-ellipsis">
    Desarrollo

  </span>


      </a>


<nav class="md-nav md-nav--secondary">





    <!--<label class="md-nav__title" for="__toc">toc.title</label>-->
    <ul class="md-nav__list" data-md-scrollfix>

        <li class="md-nav__item">
  <a href="#documentos-disponibles" class="md-nav__link">
    <span class="md-ellipsis">
      📋 Documentos Disponibles
    </span>
  </a>

    <nav class="md-nav" aria-label="📋 Documentos Disponibles">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#checklist-de-implementacion" class="md-nav__link">
    <span class="md-ellipsis">
      Checklist de Implementación
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#arquitectura-del-sistema" class="md-nav__link">
    <span class="md-ellipsis">
      Arquitectura del Sistema
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#tecnologias-utilizadas" class="md-nav__link">
    <span class="md-ellipsis">
      Tecnologías Utilizadas
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#flujo-de-autenticacion" class="md-nav__link">
    <span class="md-ellipsis">
      Flujo de Autenticación
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#proximos-pasos" class="md-nav__link">
    <span class="md-ellipsis">
      Próximos Pasos
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>





    </ul>

</nav>

    </li>










    <li class="md-nav__item">
      <a href="checklist-implementacion/" class="md-nav__link">



  <span class="md-ellipsis">
    Checklist Implementación

  </span>


      </a>
    </li>




          </ul>
        </nav>

    </li>



















    <li class="md-nav__item md-nav__item--nested">





        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4" >


          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">



  <span class="md-ellipsis">
    Sprints

  </span>


            <span class="md-nav__icon md-icon"></span>
          </label>

        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Sprints
          </label>
          <ul class="md-nav__list" data-md-scrollfix>







    <li class="md-nav__item">
      <a href="../sprints/" class="md-nav__link">



  <span class="md-ellipsis">
    Plan de Sprints

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="../sprints/plan-sprints/" class="md-nav__link">



  <span class="md-ellipsis">
    Plan de Sprints

  </span>


      </a>
    </li>




          </ul>
        </nav>

    </li>



















    <li class="md-nav__item md-nav__item--nested">





        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5" >


          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">



  <span class="md-ellipsis">
    Guías

  </span>


            <span class="md-nav__icon md-icon"></span>
          </label>

        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Guías
          </label>
          <ul class="md-nav__list" data-md-scrollfix>







    <li class="md-nav__item">
      <a href="../guias/" class="md-nav__link">



  <span class="md-ellipsis">
    Guías

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="../guias/instalaciones/" class="md-nav__link">



  <span class="md-ellipsis">
    Instalaciones

  </span>


      </a>
    </li>




          </ul>
        </nav>

    </li>



  </ul>
</nav>
                  </div>
                </div>
              </div>



              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">

<nav class="md-nav md-nav--secondary">





    <!--<label class="md-nav__title" for="__toc">toc.title</label>-->
    <ul class="md-nav__list" data-md-scrollfix>

        <li class="md-nav__item">
  <a href="#documentos-disponibles" class="md-nav__link">
    <span class="md-ellipsis">
      📋 Documentos Disponibles
    </span>
  </a>

    <nav class="md-nav" aria-label="📋 Documentos Disponibles">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#checklist-de-implementacion" class="md-nav__link">
    <span class="md-ellipsis">
      Checklist de Implementación
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#arquitectura-del-sistema" class="md-nav__link">
    <span class="md-ellipsis">
      Arquitectura del Sistema
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#tecnologias-utilizadas" class="md-nav__link">
    <span class="md-ellipsis">
      Tecnologías Utilizadas
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#flujo-de-autenticacion" class="md-nav__link">
    <span class="md-ellipsis">
      Flujo de Autenticación
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#proximos-pasos" class="md-nav__link">
    <span class="md-ellipsis">
      Próximos Pasos
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>





    </ul>

</nav>
                  </div>
                </div>
              </div>



            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">








<h1 id="desarrollo">Desarrollo<a class="headerlink" href="#desarrollo" title="Permanent link"></a></h1>
<p>Esta sección contiene documentación relacionada con el desarrollo del proyecto MsArkNet SSO.</p>
<h2 id="documentos-disponibles">📋 Documentos Disponibles<a class="headerlink" href="#documentos-disponibles" title="Permanent link"></a></h2>
<h3 id="checklist-de-implementacion"><a href="checklist-implementacion/">Checklist de Implementación</a><a class="headerlink" href="#checklist-de-implementacion" title="Permanent link"></a></h3>
<p>Lista de tareas paso a paso para implementar el sistema SSO completo, incluyendo:</p>
<ul>
<li>Infraestructura (MariaDB, Redis, dominios y TLS)</li>
<li>Esquema de base de datos y migraciones</li>
<li>Implementación OIDC Discovery + JWKS</li>
<li>Flujo Authorization Code + PKCE</li>
<li>Gestión de JWT y tokens</li>
<li>Middleware de validación</li>
<li>Sistema de logout SSO</li>
<li>RBAC y permisos</li>
<li>Observabilidad y métricas</li>
</ul>
<h3 id="arquitectura-del-sistema">Arquitectura del Sistema<a class="headerlink" href="#arquitectura-del-sistema" title="Permanent link"></a></h3>
<p>El sistema SSO está diseñado con los siguientes componentes principales:</p>
<ul>
<li><strong>Cerebro IdP</strong>: Proveedor de identidad OIDC</li>
<li><strong>Cerebro FE</strong>: Panel frontend para gestión de sesiones</li>
<li><strong>Resource Servers</strong>: Servicios backend (abogados-be, bitcoin-be)</li>
<li><strong>Auth Middleware</strong>: SDK para validación JWT</li>
</ul>
<h3 id="tecnologias-utilizadas">Tecnologías Utilizadas<a class="headerlink" href="#tecnologias-utilizadas" title="Permanent link"></a></h3>
<ul>
<li><strong>Backend</strong>: NestJS/Express + Prisma</li>
<li><strong>Frontend</strong>: React + Vite + TypeScript</li>
<li><strong>Base de datos</strong>: MariaDB</li>
<li><strong>Cache</strong>: Redis</li>
<li><strong>Proxy</strong>: Traefik</li>
<li><strong>Contenedores</strong>: Docker + Docker Compose</li>
<li><strong>Observabilidad</strong>: OpenTelemetry + Prometheus + Grafana</li>
</ul>
<h3 id="flujo-de-autenticacion">Flujo de Autenticación<a class="headerlink" href="#flujo-de-autenticacion" title="Permanent link"></a></h3>
<ol>
<li><strong>Authorization Code + PKCE</strong>: Flujo seguro para SPAs</li>
<li><strong>JWT Tokens</strong>: Con kid y aud por cliente</li>
<li><strong>Scope/Permissions</strong>: Sistema granular de permisos</li>
<li><strong>Session Management</strong>: Gestión centralizada de sesiones</li>
<li><strong>Back-channel Logout</strong>: Logout distribuido</li>
</ol>
<h3 id="proximos-pasos">Próximos Pasos<a class="headerlink" href="#proximos-pasos" title="Permanent link"></a></h3>
<p>Consulta el <a href="../sprints/plan-sprints/">Plan de Sprints</a> para ver la hoja de ruta detallada del desarrollo.</p>













              </article>
            </div>


<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>

          <button type="button" class="md-top md-icon" data-md-component="top" hidden>

  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  Back to top
</button>

      </main>

        <!--
  Copyright (c) 2016-2018 Martin Donath <<EMAIL>>

  Permission is hereby granted, free of charge, to any person obtaining a copy
  of this software and associated documentation files (the "Software"), to
  deal in the Software without restriction, including without limitation the
  rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
  sell copies of the Software, and to permit persons to whom the Software is
  furnished to do so, subject to the following conditions:

  The above copyright notice and this permission notice shall be included in
  all copies or substantial portions of the Software.

  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL THE
  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
  IN THE SOFTWARE.
-->



<!-- Application footer -->
<footer class="md-footer">

  <!-- Further information -->
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">

      <!-- Copyright and theme information -->
      <div class="md-footer-copyright" style="padding-top: 0px; padding-bottom: 0px;">

          <div class="md-footer-copyright__highlight">
            MsArkNet is licensed under the MIT License (MIT), this <a href="https://readthedocs.org/">Readthedocs.org</a> documentation uses <a href="https://www.mkdocs.org/">Mkdocs</a> and the <a href="https://squidfunk.github.io/mkdocs-material">Material theme</a>.
          </div>

      </div>

      <!-- Social links -->


<div class="md-social">

</div>

    </div>
  </div>
</footer>

    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>




      <script id="__config" type="application/json">{"base": "..", "features": ["navigation.tabs", "navigation.tabs.sticky", "navigation.sections", "navigation.expand", "navigation.path", "navigation.top", "search.highlight", "search.share", "content.code.copy", "content.code.annotate"], "search": "../assets/javascripts/workers/search.973d3a69.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": {"provider": "mike"}}</script>


      <script src="../assets/javascripts/bundle.f55a23d4.min.js"></script>


  </body>
</html>