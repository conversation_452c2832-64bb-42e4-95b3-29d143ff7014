
<!doctype html>
<html lang="en" class="no-js">
  <head>

      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">

        <meta name="description" content="Documentación completa del sistema SSO MsArkNet - Infraestructura, desarrollo y guías.">


        <meta name="author" content="MsArkNet">


        <link rel="canonical" href="https://docs.MsArkNet.me/infraestructura/ssl-certificates/">


        <link rel="prev" href="../traefik-setup/">


        <link rel="next" href="../troubleshooting/">


      <link rel="icon" href="../../images/favicon.ico">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.20">



        <title>Certificados SSL - MsArkNet SSO Documentation</title>



      <link rel="stylesheet" href="../../assets/stylesheets/main.e53b48f4.min.css">


        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">












        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>



    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>

  <!--Empty the footer analytics bloc as we need it the headers -->



  <link rel="stylesheet" href="../../assets/stylesheets/nethereum_custom.css">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.1/css/all.css" integrity="sha384-gfdkjb5BdAXd+lj+gudLWI+BXq4IuLW5IT+brZEZsLFm++aCMlF1V92rMkPaX4PP" crossorigin="anonymous">
  <script
      src="https://code.jquery.com/jquery-1.12.4.min.js"
      integrity="sha256-ZosEbRLbNQzLpnKIkEdrPv7lOy9C27hHQ+Xp8a4MxAQ="
      crossorigin="anonymous"></script>
  <script async src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-MML-AM_CHTML"></script>


  </head>









    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="teal" data-md-color-accent="teal">


    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">


        <a href="#certificados-ssltls" class="md-skip">
          Skip to content
        </a>

    </div>
    <div data-md-component="announce">

    </div>

      <div data-md-color-scheme="default" data-md-component="outdated" hidden>

      </div>






<header class="md-header md-header--shadow md-header--lifted" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="MsArkNet SSO Documentation" class="md-header__button md-logo" aria-label="MsArkNet SSO Documentation" data-md-component="logo">

  <img src="../../assets/logo.png" alt="logo">

    </a>
    <label class="md-header__button md-icon" for="__drawer">

      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            MsArkNet SSO Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">

              Certificados SSL

          </span>
        </div>
      </div>
    </div>


        <form class="md-header__option" data-md-component="palette">




    <input class="md-option" data-md-color-media="" data-md-color-scheme="default" data-md-color-primary="teal" data-md-color-accent="teal"  aria-label="Switch to dark mode"  type="radio" name="__palette" id="__palette_0">

      <label class="md-header__button md-icon" title="Switch to dark mode" for="__palette_1" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>





    <input class="md-option" data-md-color-media="" data-md-color-scheme="slate" data-md-color-primary="teal" data-md-color-accent="teal"  aria-label="Switch to light mode"  type="radio" name="__palette" id="__palette_1">

      <label class="md-header__button md-icon" title="Switch to light mode" for="__palette_0" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>


</form>



      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>





        <label class="md-header__button md-icon" for="__search">

          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">

        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>

        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">

          <a href="javascript:void(0)" class="md-search__icon md-icon" title="Share" aria-label="Share" data-clipboard data-clipboard-text="" data-md-component="search-share" tabindex="-1">

            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.15c-.05.21-.08.43-.08.66 0 1.61 1.31 2.91 2.92 2.91s2.92-1.3 2.92-2.91A2.92 2.92 0 0 0 18 16.08"/></svg>
          </a>

        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">

          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>

    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>



      <div class="md-header__source">
        <!--
  Copyright (c) 2016-2019 Martin Donath <<EMAIL>>

  Permission is hereby granted, free of charge, to any person obtaining a copy
  of this software and associated documentation files (the "Software"), to
  deal in the Software without restriction, including without limitation the
  rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
  sell copies of the Software, and to permit persons to whom the Software is
  furnished to do so, subject to the following conditions:

  The above copyright notice and this permission notice shall be included in
  all copies or substantial portions of the Software.

  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL THE
  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
  IN THE SOFTWARE.
-->



<!--
  Check whether the repository is hosted on one of the supported code hosting
  platforms (GitHub, GitLab or Bitbucket) to show icon.
-->





<!-- Repository containing source -->
<a href="https://github.com/MsArkNet/webs" title="source.link.title"
    class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg viewBox="0 0 24 24" width="24" height="24">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>

  <div class="md-source__repository">
    MsArkNet/webs
  </div>
</a>
      </div>

  </nav>



<nav class="md-tabs" aria-label="Tabs" data-md-component="tabs">
  <div class="md-grid">
    <ul class="md-tabs__list">






    <li class="md-tabs__item">
      <a href="../.." class="md-tabs__link">





  Inicio

      </a>
    </li>












      <li class="md-tabs__item md-tabs__item--active">
        <a href="../" class="md-tabs__link">



  Infraestructura

        </a>
      </li>











      <li class="md-tabs__item">
        <a href="../../desarrollo/" class="md-tabs__link">



  Desarrollo

        </a>
      </li>











      <li class="md-tabs__item">
        <a href="../../sprints/" class="md-tabs__link">



  Sprints

        </a>
      </li>











      <li class="md-tabs__item">
        <a href="../../guias/" class="md-tabs__link">



  Guías

        </a>
      </li>




    </ul>
  </div>
</nav>


</header>

    <div class="md-container" data-md-component="container">




      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">



              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">






<nav class="md-nav md-nav--primary md-nav--lifted" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="MsArkNet SSO Documentation" class="md-nav__button md-logo" aria-label="MsArkNet SSO Documentation" data-md-component="logo">

  <img src="../../assets/logo.png" alt="logo">

    </a>
    MsArkNet SSO Documentation
  </label>

    <div class="md-nav__source">
      <!--
  Copyright (c) 2016-2019 Martin Donath <<EMAIL>>

  Permission is hereby granted, free of charge, to any person obtaining a copy
  of this software and associated documentation files (the "Software"), to
  deal in the Software without restriction, including without limitation the
  rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
  sell copies of the Software, and to permit persons to whom the Software is
  furnished to do so, subject to the following conditions:

  The above copyright notice and this permission notice shall be included in
  all copies or substantial portions of the Software.

  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL THE
  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
  IN THE SOFTWARE.
-->



<!--
  Check whether the repository is hosted on one of the supported code hosting
  platforms (GitHub, GitLab or Bitbucket) to show icon.
-->





<!-- Repository containing source -->
<a href="https://github.com/MsArkNet/webs" title="source.link.title"
    class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg viewBox="0 0 24 24" width="24" height="24">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>

  <div class="md-source__repository">
    MsArkNet/webs
  </div>
</a>
    </div>

  <ul class="md-nav__list" data-md-scrollfix>







    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">



  <span class="md-ellipsis">
    Inicio

  </span>


      </a>
    </li>
























    <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">



        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_2" checked>


          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="">



  <span class="md-ellipsis">
    Infraestructura

  </span>


            <span class="md-nav__icon md-icon"></span>
          </label>

        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            Infraestructura
          </label>
          <ul class="md-nav__list" data-md-scrollfix>







    <li class="md-nav__item">
      <a href="../" class="md-nav__link">



  <span class="md-ellipsis">
    MsArkNet - Entorno Docker con Traefik

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="../traefik-setup/" class="md-nav__link">



  <span class="md-ellipsis">
    Configuración Traefik

  </span>


      </a>
    </li>












    <li class="md-nav__item md-nav__item--active">

      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">





        <label class="md-nav__link md-nav__link--active" for="__toc">



  <span class="md-ellipsis">
    Certificados SSL

  </span>


          <span class="md-nav__icon md-icon"></span>
        </label>

      <a href="./" class="md-nav__link md-nav__link--active">



  <span class="md-ellipsis">
    Certificados SSL

  </span>


      </a>


<nav class="md-nav md-nav--secondary">





    <!--<label class="md-nav__title" for="__toc">toc.title</label>-->
    <ul class="md-nav__list" data-md-scrollfix>

        <li class="md-nav__item">
  <a href="#vision-general" class="md-nav__link">
    <span class="md-ellipsis">
      🔒 Visión General
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#herramientas-utilizadas" class="md-nav__link">
    <span class="md-ellipsis">
      🛠️ Herramientas Utilizadas
    </span>
  </a>

    <nav class="md-nav" aria-label="🛠️ Herramientas Utilizadas">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#mkcert" class="md-nav__link">
    <span class="md-ellipsis">
      mkcert
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>

        <li class="md-nav__item">
  <a href="#configuracion-rapida" class="md-nav__link">
    <span class="md-ellipsis">
      ⚡ Configuración Rápida
    </span>
  </a>

    <nav class="md-nav" aria-label="⚡ Configuración Rápida">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#1-instalar-y-configurar-mkcert" class="md-nav__link">
    <span class="md-ellipsis">
      1. Instalar y configurar mkcert
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#2-generar-certificados-para-MsArkNet" class="md-nav__link">
    <span class="md-ellipsis">
      2. Generar certificados para MsArkNet
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#3-configurar-traefik" class="md-nav__link">
    <span class="md-ellipsis">
      3. Configurar Traefik
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>

        <li class="md-nav__item">
  <a href="#estructura-de-certificados" class="md-nav__link">
    <span class="md-ellipsis">
      📁 Estructura de Certificados
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#configuracion-manual" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Configuración Manual
    </span>
  </a>

    <nav class="md-nav" aria-label="🔧 Configuración Manual">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#generar-certificados-con-openssl" class="md-nav__link">
    <span class="md-ellipsis">
      Generar certificados con OpenSSL
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>

        <li class="md-nav__item">
  <a href="#configuracion-del-navegador" class="md-nav__link">
    <span class="md-ellipsis">
      🌐 Configuración del Navegador
    </span>
  </a>

    <nav class="md-nav" aria-label="🌐 Configuración del Navegador">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#chromechromium" class="md-nav__link">
    <span class="md-ellipsis">
      Chrome/Chromium
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#firefox" class="md-nav__link">
    <span class="md-ellipsis">
      Firefox
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#safari-macos" class="md-nav__link">
    <span class="md-ellipsis">
      Safari (macOS)
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>

        <li class="md-nav__item">
  <a href="#renovacion-de-certificados" class="md-nav__link">
    <span class="md-ellipsis">
      🔄 Renovación de Certificados
    </span>
  </a>

    <nav class="md-nav" aria-label="🔄 Renovación de Certificados">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#automatica-con-mkcert" class="md-nav__link">
    <span class="md-ellipsis">
      Automática con mkcert
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#script-de-renovacion-automatica" class="md-nav__link">
    <span class="md-ellipsis">
      Script de renovación automática
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>

        <li class="md-nav__item">
  <a href="#verificacion-y-testing" class="md-nav__link">
    <span class="md-ellipsis">
      🔍 Verificación y Testing
    </span>
  </a>

    <nav class="md-nav" aria-label="🔍 Verificación y Testing">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#verificar-certificados" class="md-nav__link">
    <span class="md-ellipsis">
      Verificar certificados
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#debugging-de-problemas-ssl" class="md-nav__link">
    <span class="md-ellipsis">
      Debugging de problemas SSL
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>

        <li class="md-nav__item">
  <a href="#consideraciones-de-seguridad" class="md-nav__link">
    <span class="md-ellipsis">
      ⚠️ Consideraciones de Seguridad
    </span>
  </a>

    <nav class="md-nav" aria-label="⚠️ Consideraciones de Seguridad">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#desarrollo-vs-produccion" class="md-nav__link">
    <span class="md-ellipsis">
      Desarrollo vs Producción
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#buenas-practicas" class="md-nav__link">
    <span class="md-ellipsis">
      Buenas Prácticas
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>

        <li class="md-nav__item">
  <a href="#enlaces-utiles" class="md-nav__link">
    <span class="md-ellipsis">
      🔗 Enlaces Útiles
    </span>
  </a>

</li>





    </ul>

</nav>

    </li>










    <li class="md-nav__item">
      <a href="../troubleshooting/" class="md-nav__link">



  <span class="md-ellipsis">
    Troubleshooting

  </span>


      </a>
    </li>




          </ul>
        </nav>

    </li>



















    <li class="md-nav__item md-nav__item--nested">





        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_3" >


          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">



  <span class="md-ellipsis">
    Desarrollo

  </span>


            <span class="md-nav__icon md-icon"></span>
          </label>

        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Desarrollo
          </label>
          <ul class="md-nav__list" data-md-scrollfix>







    <li class="md-nav__item">
      <a href="../../desarrollo/" class="md-nav__link">



  <span class="md-ellipsis">
    Desarrollo

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="../../desarrollo/checklist-implementacion/" class="md-nav__link">



  <span class="md-ellipsis">
    Checklist Implementación

  </span>


      </a>
    </li>




          </ul>
        </nav>

    </li>



















    <li class="md-nav__item md-nav__item--nested">





        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4" >


          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="0">



  <span class="md-ellipsis">
    Sprints

  </span>


            <span class="md-nav__icon md-icon"></span>
          </label>

        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Sprints
          </label>
          <ul class="md-nav__list" data-md-scrollfix>







    <li class="md-nav__item">
      <a href="../../sprints/" class="md-nav__link">



  <span class="md-ellipsis">
    Plan de Sprints

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="../../sprints/plan-sprints/" class="md-nav__link">



  <span class="md-ellipsis">
    Plan de Sprints

  </span>


      </a>
    </li>




          </ul>
        </nav>

    </li>



















    <li class="md-nav__item md-nav__item--nested">





        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5" >


          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">



  <span class="md-ellipsis">
    Guías

  </span>


            <span class="md-nav__icon md-icon"></span>
          </label>

        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Guías
          </label>
          <ul class="md-nav__list" data-md-scrollfix>







    <li class="md-nav__item">
      <a href="../../guias/" class="md-nav__link">



  <span class="md-ellipsis">
    Guías

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="../../guias/instalaciones/" class="md-nav__link">



  <span class="md-ellipsis">
    Instalaciones

  </span>


      </a>
    </li>




          </ul>
        </nav>

    </li>



  </ul>
</nav>
                  </div>
                </div>
              </div>



              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">

<nav class="md-nav md-nav--secondary">





    <!--<label class="md-nav__title" for="__toc">toc.title</label>-->
    <ul class="md-nav__list" data-md-scrollfix>

        <li class="md-nav__item">
  <a href="#vision-general" class="md-nav__link">
    <span class="md-ellipsis">
      🔒 Visión General
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#herramientas-utilizadas" class="md-nav__link">
    <span class="md-ellipsis">
      🛠️ Herramientas Utilizadas
    </span>
  </a>

    <nav class="md-nav" aria-label="🛠️ Herramientas Utilizadas">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#mkcert" class="md-nav__link">
    <span class="md-ellipsis">
      mkcert
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>

        <li class="md-nav__item">
  <a href="#configuracion-rapida" class="md-nav__link">
    <span class="md-ellipsis">
      ⚡ Configuración Rápida
    </span>
  </a>

    <nav class="md-nav" aria-label="⚡ Configuración Rápida">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#1-instalar-y-configurar-mkcert" class="md-nav__link">
    <span class="md-ellipsis">
      1. Instalar y configurar mkcert
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#2-generar-certificados-para-MsArkNet" class="md-nav__link">
    <span class="md-ellipsis">
      2. Generar certificados para MsArkNet
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#3-configurar-traefik" class="md-nav__link">
    <span class="md-ellipsis">
      3. Configurar Traefik
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>

        <li class="md-nav__item">
  <a href="#estructura-de-certificados" class="md-nav__link">
    <span class="md-ellipsis">
      📁 Estructura de Certificados
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#configuracion-manual" class="md-nav__link">
    <span class="md-ellipsis">
      🔧 Configuración Manual
    </span>
  </a>

    <nav class="md-nav" aria-label="🔧 Configuración Manual">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#generar-certificados-con-openssl" class="md-nav__link">
    <span class="md-ellipsis">
      Generar certificados con OpenSSL
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>

        <li class="md-nav__item">
  <a href="#configuracion-del-navegador" class="md-nav__link">
    <span class="md-ellipsis">
      🌐 Configuración del Navegador
    </span>
  </a>

    <nav class="md-nav" aria-label="🌐 Configuración del Navegador">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#chromechromium" class="md-nav__link">
    <span class="md-ellipsis">
      Chrome/Chromium
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#firefox" class="md-nav__link">
    <span class="md-ellipsis">
      Firefox
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#safari-macos" class="md-nav__link">
    <span class="md-ellipsis">
      Safari (macOS)
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>

        <li class="md-nav__item">
  <a href="#renovacion-de-certificados" class="md-nav__link">
    <span class="md-ellipsis">
      🔄 Renovación de Certificados
    </span>
  </a>

    <nav class="md-nav" aria-label="🔄 Renovación de Certificados">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#automatica-con-mkcert" class="md-nav__link">
    <span class="md-ellipsis">
      Automática con mkcert
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#script-de-renovacion-automatica" class="md-nav__link">
    <span class="md-ellipsis">
      Script de renovación automática
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>

        <li class="md-nav__item">
  <a href="#verificacion-y-testing" class="md-nav__link">
    <span class="md-ellipsis">
      🔍 Verificación y Testing
    </span>
  </a>

    <nav class="md-nav" aria-label="🔍 Verificación y Testing">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#verificar-certificados" class="md-nav__link">
    <span class="md-ellipsis">
      Verificar certificados
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#debugging-de-problemas-ssl" class="md-nav__link">
    <span class="md-ellipsis">
      Debugging de problemas SSL
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>

        <li class="md-nav__item">
  <a href="#consideraciones-de-seguridad" class="md-nav__link">
    <span class="md-ellipsis">
      ⚠️ Consideraciones de Seguridad
    </span>
  </a>

    <nav class="md-nav" aria-label="⚠️ Consideraciones de Seguridad">
      <ul class="md-nav__list">

          <li class="md-nav__item">
  <a href="#desarrollo-vs-produccion" class="md-nav__link">
    <span class="md-ellipsis">
      Desarrollo vs Producción
    </span>
  </a>

</li>

          <li class="md-nav__item">
  <a href="#buenas-practicas" class="md-nav__link">
    <span class="md-ellipsis">
      Buenas Prácticas
    </span>
  </a>

</li>

      </ul>
    </nav>

</li>

        <li class="md-nav__item">
  <a href="#enlaces-utiles" class="md-nav__link">
    <span class="md-ellipsis">
      🔗 Enlaces Útiles
    </span>
  </a>

</li>





    </ul>

</nav>
                  </div>
                </div>
              </div>



            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">








<h1 id="certificados-ssltls">Certificados SSL/TLS<a class="headerlink" href="#certificados-ssltls" title="Permanent link"></a></h1>
<p>Guía completa para la gestión de certificados SSL/TLS en el entorno de desarrollo MsArkNet.</p>
<h2 id="vision-general">🔒 Visión General<a class="headerlink" href="#vision-general" title="Permanent link"></a></h2>
<p>Los certificados SSL/TLS son esenciales para:</p>
<ul>
<li><strong>Desarrollo local seguro</strong> con HTTPS</li>
<li><strong>Simulación del entorno de producción</strong></li>
<li><strong>Compatibilidad con navegadores modernos</strong></li>
<li><strong>Testing de funcionalidades que requieren HTTPS</strong></li>
</ul>
<h2 id="herramientas-utilizadas">🛠️ Herramientas Utilizadas<a class="headerlink" href="#herramientas-utilizadas" title="Permanent link"></a></h2>
<h3 id="mkcert">mkcert<a class="headerlink" href="#mkcert" title="Permanent link"></a></h3>
<p>Herramienta para generar certificados SSL locales de confianza.</p>
<div class="highlight"><pre><span></span><code><span class="c1"># Instalación en Ubuntu/Debian</span>
sudo<span class="w"> </span>apt<span class="w"> </span>install<span class="w"> </span>-y<span class="w"> </span>mkcert<span class="w"> </span>libnss3-tools

<span class="c1"># Instalación en macOS</span>
brew<span class="w"> </span>install<span class="w"> </span>mkcert

<span class="c1"># Instalación en Windows</span>
choco<span class="w"> </span>install<span class="w"> </span>mkcert
</code></pre></div>
<h2 id="configuracion-rapida">⚡ Configuración Rápida<a class="headerlink" href="#configuracion-rapida" title="Permanent link"></a></h2>
<h3 id="1-instalar-y-configurar-mkcert">1. Instalar y configurar mkcert<a class="headerlink" href="#1-instalar-y-configurar-mkcert" title="Permanent link"></a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Instalar la CA local</span>
mkcert<span class="w"> </span>-install

<span class="c1"># Verificar instalación</span>
mkcert<span class="w"> </span>-CAROOT
</code></pre></div>
<h3 id="2-generar-certificados-para-MsArkNet">2. Generar certificados para MsArkNet<a class="headerlink" href="#2-generar-certificados-para-MsArkNet" title="Permanent link"></a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Generar certificados para todos los subdominios</span>
mkcert<span class="w"> </span>-key-file<span class="w"> </span>traefik/dynamic/certs/MsArkNet-key.pem<span class="w"> </span><span class="se">\</span>
<span class="w">       </span>-cert-file<span class="w"> </span>traefik/dynamic/certs/MsArkNet-cert.pem<span class="w"> </span><span class="se">\</span>
<span class="w">       </span>MsArkNet.me<span class="w"> </span><span class="se">\</span>
<span class="w">       </span><span class="s2">&quot;*.MsArkNet.me&quot;</span><span class="w"> </span><span class="se">\</span>
<span class="w">       </span>grafana.MsArkNet.me<span class="w"> </span><span class="se">\</span>
<span class="w">       </span>prom.MsArkNet.me<span class="w"> </span><span class="se">\</span>
<span class="w">       </span>traefik.MsArkNet.me<span class="w"> </span><span class="se">\</span>
<span class="w">       </span>portainer.MsArkNet.me<span class="w"> </span><span class="se">\</span>
<span class="w">       </span>docs.MsArkNet.me
</code></pre></div>
<h3 id="3-configurar-traefik">3. Configurar Traefik<a class="headerlink" href="#3-configurar-traefik" title="Permanent link"></a></h3>
<p>Los certificados se configuran automáticamente en Traefik a través del archivo <code>dynamic/tls.yml</code>:</p>
<div class="highlight"><pre><span></span><code><span class="nt">tls</span><span class="p">:</span>
<span class="w">  </span><span class="nt">certificates</span><span class="p">:</span>
<span class="w">    </span><span class="p p-Indicator">-</span><span class="w"> </span><span class="nt">certFile</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/etc/traefik/certs/MsArkNet-cert.pem</span>
<span class="w">      </span><span class="nt">keyFile</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">/etc/traefik/certs/MsArkNet-key.pem</span>
</code></pre></div>
<h2 id="estructura-de-certificados">📁 Estructura de Certificados<a class="headerlink" href="#estructura-de-certificados" title="Permanent link"></a></h2>
<div class="highlight"><pre><span></span><code>certs/
├── MsArkNet-cert.pem     # Certificado público
├── MsArkNet-key.pem      # Clave privada
└── MsArkNet.conf         # Configuración (opcional)
</code></pre></div>
<h2 id="configuracion-manual">🔧 Configuración Manual<a class="headerlink" href="#configuracion-manual" title="Permanent link"></a></h2>
<h3 id="generar-certificados-con-openssl">Generar certificados con OpenSSL<a class="headerlink" href="#generar-certificados-con-openssl" title="Permanent link"></a></h3>
<p>Si prefieres usar OpenSSL directamente:</p>
<div class="highlight"><pre><span></span><code><span class="c1"># Crear clave privada</span>
openssl<span class="w"> </span>genrsa<span class="w"> </span>-out<span class="w"> </span>MsArkNet-key.pem<span class="w"> </span><span class="m">2048</span>

<span class="c1"># Crear CSR (Certificate Signing Request)</span>
openssl<span class="w"> </span>req<span class="w"> </span>-new<span class="w"> </span>-key<span class="w"> </span>MsArkNet-key.pem<span class="w"> </span>-out<span class="w"> </span>MsArkNet.csr<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-subj<span class="w"> </span><span class="s2">&quot;/C=ES/ST=Madrid/L=Madrid/O=MsArkNet/CN=MsArkNet.me&quot;</span>

<span class="c1"># Generar certificado autofirmado</span>
openssl<span class="w"> </span>x509<span class="w"> </span>-req<span class="w"> </span>-in<span class="w"> </span>MsArkNet.csr<span class="w"> </span>-signkey<span class="w"> </span>MsArkNet-key.pem<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-out<span class="w"> </span>MsArkNet-cert.pem<span class="w"> </span>-days<span class="w"> </span><span class="m">365</span><span class="w"> </span><span class="se">\</span>
<span class="w">  </span>-extensions<span class="w"> </span>v3_req<span class="w"> </span>-extfile<span class="w"> </span>&lt;<span class="o">(</span>cat<span class="w"> </span><span class="s">&lt;&lt;EOF</span>
<span class="s">[v3_req]</span>
<span class="s">subjectAltName = @alt_names</span>
<span class="s">[alt_names]</span>
<span class="s">DNS.1 = MsArkNet.me</span>
<span class="s">DNS.2 = *.MsArkNet.me</span>
<span class="s">DNS.3 = grafana.MsArkNet.me</span>
<span class="s">DNS.4 = prom.MsArkNet.me</span>
<span class="s">DNS.5 = traefik.MsArkNet.me</span>
<span class="s">EOF</span>
<span class="o">)</span>
</code></pre></div>
<h2 id="configuracion-del-navegador">🌐 Configuración del Navegador<a class="headerlink" href="#configuracion-del-navegador" title="Permanent link"></a></h2>
<h3 id="chromechromium">Chrome/Chromium<a class="headerlink" href="#chromechromium" title="Permanent link"></a></h3>
<ol>
<li>Ir a <code>chrome://settings/certificates</code></li>
<li>Pestaña &ldquo;Authorities&rdquo;</li>
<li>Importar el certificado CA de mkcert</li>
<li>Marcar &ldquo;Trust this certificate for identifying websites&rdquo;</li>
</ol>
<h3 id="firefox">Firefox<a class="headerlink" href="#firefox" title="Permanent link"></a></h3>
<ol>
<li>Ir a <code>about:preferences#privacy</code></li>
<li>Sección &ldquo;Certificates&rdquo; → &ldquo;View Certificates&rdquo;</li>
<li>Pestaña &ldquo;Authorities&rdquo;</li>
<li>Importar el certificado CA de mkcert</li>
</ol>
<h3 id="safari-macos">Safari (macOS)<a class="headerlink" href="#safari-macos" title="Permanent link"></a></h3>
<ol>
<li>Abrir &ldquo;Keychain Access&rdquo;</li>
<li>Buscar &ldquo;mkcert&rdquo;</li>
<li>Doble clic en el certificado</li>
<li>Expandir &ldquo;Trust&rdquo;</li>
<li>Cambiar &ldquo;When using this certificate&rdquo; a &ldquo;Always Trust&rdquo;</li>
</ol>
<h2 id="renovacion-de-certificados">🔄 Renovación de Certificados<a class="headerlink" href="#renovacion-de-certificados" title="Permanent link"></a></h2>
<h3 id="automatica-con-mkcert">Automática con mkcert<a class="headerlink" href="#automatica-con-mkcert" title="Permanent link"></a></h3>
<p>Los certificados generados con mkcert tienen una validez de 2 años y 3 meses. Para renovar:</p>
<div class="highlight"><pre><span></span><code><span class="c1"># Regenerar certificados</span>
./generate-certs.sh

<span class="c1"># Reiniciar Traefik para cargar nuevos certificados</span>
docker<span class="w"> </span>compose<span class="w"> </span>restart<span class="w"> </span>traefik
</code></pre></div>
<h3 id="script-de-renovacion-automatica">Script de renovación automática<a class="headerlink" href="#script-de-renovacion-automatica" title="Permanent link"></a></h3>
<div class="highlight"><pre><span></span><code><span class="ch">#!/bin/bash</span>
<span class="c1"># renew-certs.sh</span>

<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;🔄 Renovando certificados SSL...&quot;</span>

<span class="c1"># Backup de certificados actuales</span>
cp<span class="w"> </span>certs/MsArkNet-cert.pem<span class="w"> </span>certs/MsArkNet-cert.pem.backup
cp<span class="w"> </span>certs/MsArkNet-key.pem<span class="w"> </span>certs/MsArkNet-key.pem.backup

<span class="c1"># Generar nuevos certificados</span>
mkcert<span class="w"> </span>-key-file<span class="w"> </span>certs/MsArkNet-key.pem<span class="w"> </span><span class="se">\</span>
<span class="w">       </span>-cert-file<span class="w"> </span>certs/MsArkNet-cert.pem<span class="w"> </span><span class="se">\</span>
<span class="w">       </span>MsArkNet.me<span class="w"> </span><span class="s2">&quot;*.MsArkNet.me&quot;</span>

<span class="c1"># Reiniciar Traefik</span>
docker<span class="w"> </span>compose<span class="w"> </span>restart<span class="w"> </span>traefik

<span class="nb">echo</span><span class="w"> </span><span class="s2">&quot;✅ Certificados renovados correctamente&quot;</span>
</code></pre></div>
<h2 id="verificacion-y-testing">🔍 Verificación y Testing<a class="headerlink" href="#verificacion-y-testing" title="Permanent link"></a></h2>
<h3 id="verificar-certificados">Verificar certificados<a class="headerlink" href="#verificar-certificados" title="Permanent link"></a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Verificar información del certificado</span>
openssl<span class="w"> </span>x509<span class="w"> </span>-in<span class="w"> </span>certs/MsArkNet-cert.pem<span class="w"> </span>-text<span class="w"> </span>-noout

<span class="c1"># Verificar fechas de validez</span>
openssl<span class="w"> </span>x509<span class="w"> </span>-in<span class="w"> </span>certs/MsArkNet-cert.pem<span class="w"> </span>-dates<span class="w"> </span>-noout

<span class="c1"># Test de conectividad HTTPS</span>
curl<span class="w"> </span>-k<span class="w"> </span>-I<span class="w"> </span>https://MsArkNet.me
</code></pre></div>
<h3 id="debugging-de-problemas-ssl">Debugging de problemas SSL<a class="headerlink" href="#debugging-de-problemas-ssl" title="Permanent link"></a></h3>
<div class="highlight"><pre><span></span><code><span class="c1"># Test detallado de SSL</span>
openssl<span class="w"> </span>s_client<span class="w"> </span>-connect<span class="w"> </span>MsArkNet.me:443<span class="w"> </span>-servername<span class="w"> </span>MsArkNet.me

<span class="c1"># Verificar configuración de Traefik</span>
curl<span class="w"> </span>-s<span class="w"> </span>http://localhost:8080/api/rawdata<span class="w"> </span><span class="p">|</span><span class="w"> </span>jq<span class="w"> </span><span class="s1">&#39;.tls&#39;</span>
</code></pre></div>
<h2 id="consideraciones-de-seguridad">⚠️ Consideraciones de Seguridad<a class="headerlink" href="#consideraciones-de-seguridad" title="Permanent link"></a></h2>
<h3 id="desarrollo-vs-produccion">Desarrollo vs Producción<a class="headerlink" href="#desarrollo-vs-produccion" title="Permanent link"></a></h3>
<table>
<thead>
<tr>
<th>Aspecto</th>
<th>Desarrollo</th>
<th>Producción</th>
</tr>
</thead>
<tbody>
<tr>
<td><strong>Certificados</strong></td>
<td>Autofirmados (mkcert)</td>
<td>Let&rsquo;s Encrypt / CA comercial</td>
</tr>
<tr>
<td><strong>Validez</strong></td>
<td>2+ años</td>
<td>90 días (Let&rsquo;s Encrypt)</td>
</tr>
<tr>
<td><strong>Renovación</strong></td>
<td>Manual</td>
<td>Automática</td>
</tr>
<tr>
<td><strong>Confianza</strong></td>
<td>Solo local</td>
<td>Globalmente confiable</td>
</tr>
</tbody>
</table>
<h3 id="buenas-practicas">Buenas Prácticas<a class="headerlink" href="#buenas-practicas" title="Permanent link"></a></h3>
<ul>
<li>✅ <strong>Nunca</strong> commitear claves privadas al repositorio</li>
<li>✅ Usar <code>.gitignore</code> para excluir archivos de certificados</li>
<li>✅ Rotar certificados regularmente</li>
<li>✅ Usar certificados diferentes para cada entorno</li>
<li>✅ Monitorizar fechas de expiración</li>
</ul>
<h2 id="enlaces-utiles">🔗 Enlaces Útiles<a class="headerlink" href="#enlaces-utiles" title="Permanent link"></a></h2>
<ul>
<li><a href="https://github.com/FiloSottile/mkcert">mkcert GitHub</a></li>
<li><a href="https://doc.traefik.io/traefik/https/tls/">Traefik TLS Documentation</a></li>
<li><a href="https://letsencrypt.org/">Let&rsquo;s Encrypt</a></li>
<li><a href="https://www.openssl.org/docs/">OpenSSL Documentation</a></li>
</ul>













              </article>
            </div>


<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>

          <button type="button" class="md-top md-icon" data-md-component="top" hidden>

  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  Back to top
</button>

      </main>

        <!--
  Copyright (c) 2016-2018 Martin Donath <<EMAIL>>

  Permission is hereby granted, free of charge, to any person obtaining a copy
  of this software and associated documentation files (the "Software"), to
  deal in the Software without restriction, including without limitation the
  rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
  sell copies of the Software, and to permit persons to whom the Software is
  furnished to do so, subject to the following conditions:

  The above copyright notice and this permission notice shall be included in
  all copies or substantial portions of the Software.

  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL THE
  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
  IN THE SOFTWARE.
-->



<!-- Application footer -->
<footer class="md-footer">

  <!-- Further information -->
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">

      <!-- Copyright and theme information -->
      <div class="md-footer-copyright" style="padding-top: 0px; padding-bottom: 0px;">

          <div class="md-footer-copyright__highlight">
            MsArkNet is licensed under the MIT License (MIT), this <a href="https://readthedocs.org/">Readthedocs.org</a> documentation uses <a href="https://www.mkdocs.org/">Mkdocs</a> and the <a href="https://squidfunk.github.io/mkdocs-material">Material theme</a>.
          </div>

      </div>

      <!-- Social links -->


<div class="md-social">

</div>

    </div>
  </div>
</footer>

    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>




      <script id="__config" type="application/json">{"base": "../..", "features": ["navigation.tabs", "navigation.tabs.sticky", "navigation.sections", "navigation.expand", "navigation.path", "navigation.top", "search.highlight", "search.share", "content.code.copy", "content.code.annotate"], "search": "../../assets/javascripts/workers/search.973d3a69.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": {"provider": "mike"}}</script>


      <script src="../../assets/javascripts/bundle.f55a23d4.min.js"></script>


  </body>
</html>