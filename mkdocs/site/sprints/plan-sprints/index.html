
<!doctype html>
<html lang="en" class="no-js">
  <head>

      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">

        <meta name="description" content="Documentación completa del sistema SSO MsArkNet - Infraestructura, desarrollo y guías.">


        <meta name="author" content="MsArkNet">


        <link rel="canonical" href="https://docs.MsArkNet.me/sprints/plan-sprints/">


        <link rel="prev" href="../">


        <link rel="next" href="../../guias/">


      <link rel="icon" href="../../images/favicon.ico">
      <meta name="generator" content="mkdocs-1.6.1, mkdocs-material-9.6.20">



        <title>Plan de Sprints - MsArkNet SSO Documentation</title>



      <link rel="stylesheet" href="../../assets/stylesheets/main.e53b48f4.min.css">


        <link rel="stylesheet" href="../../assets/stylesheets/palette.06af60db.min.css">












        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>



    <script>__md_scope=new URL("../..",location),__md_hash=e=>[...e].reduce(((e,_)=>(e<<5)-e+_.charCodeAt(0)),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>

  <!--Empty the footer analytics bloc as we need it the headers -->



  <link rel="stylesheet" href="../../assets/stylesheets/nethereum_custom.css">
  <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.6.1/css/all.css" integrity="sha384-gfdkjb5BdAXd+lj+gudLWI+BXq4IuLW5IT+brZEZsLFm++aCMlF1V92rMkPaX4PP" crossorigin="anonymous">
  <script
      src="https://code.jquery.com/jquery-1.12.4.min.js"
      integrity="sha256-ZosEbRLbNQzLpnKIkEdrPv7lOy9C27hHQ+Xp8a4MxAQ="
      crossorigin="anonymous"></script>
  <script async src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-MML-AM_CHTML"></script>


  </head>









    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="teal" data-md-color-accent="teal">


    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">


        <a href="#plan-de-sprints-sso-cerebro-fe-be-en-paralelo" class="md-skip">
          Skip to content
        </a>

    </div>
    <div data-md-component="announce">

    </div>

      <div data-md-color-scheme="default" data-md-component="outdated" hidden>

      </div>






<header class="md-header md-header--shadow md-header--lifted" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href="../.." title="MsArkNet SSO Documentation" class="md-header__button md-logo" aria-label="MsArkNet SSO Documentation" data-md-component="logo">

  <img src="../../assets/logo.png" alt="logo">

    </a>
    <label class="md-header__button md-icon" for="__drawer">

      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3zm0 5h18v2H3zm0 5h18v2H3z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            MsArkNet SSO Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">

              Plan de Sprints

          </span>
        </div>
      </div>
    </div>


        <form class="md-header__option" data-md-component="palette">




    <input class="md-option" data-md-color-media="" data-md-color-scheme="default" data-md-color-primary="teal" data-md-color-accent="teal"  aria-label="Switch to dark mode"  type="radio" name="__palette" id="__palette_0">

      <label class="md-header__button md-icon" title="Switch to dark mode" for="__palette_1" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a4 4 0 0 0-4 4 4 4 0 0 0 4 4 4 4 0 0 0 4-4 4 4 0 0 0-4-4m0 10a6 6 0 0 1-6-6 6 6 0 0 1 6-6 6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>





    <input class="md-option" data-md-color-media="" data-md-color-scheme="slate" data-md-color-primary="teal" data-md-color-accent="teal"  aria-label="Switch to light mode"  type="radio" name="__palette" id="__palette_1">

      <label class="md-header__button md-icon" title="Switch to light mode" for="__palette_0" hidden>
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 18c-.89 0-1.74-.2-2.5-.55C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.45C10.26 6.2 11.11 6 12 6a6 6 0 0 1 6 6 6 6 0 0 1-6 6m8-9.31V4h-4.69L12 .69 8.69 4H4v4.69L.69 12 4 15.31V20h4.69L12 23.31 15.31 20H20v-4.69L23.31 12z"/></svg>
      </label>


</form>



      <script>var palette=__md_get("__palette");if(palette&&palette.color){if("(prefers-color-scheme)"===palette.color.media){var media=matchMedia("(prefers-color-scheme: light)"),input=document.querySelector(media.matches?"[data-md-color-media='(prefers-color-scheme: light)']":"[data-md-color-media='(prefers-color-scheme: dark)']");palette.color.media=input.getAttribute("data-md-color-media"),palette.color.scheme=input.getAttribute("data-md-color-scheme"),palette.color.primary=input.getAttribute("data-md-color-primary"),palette.color.accent=input.getAttribute("data-md-color-accent")}for(var[key,value]of Object.entries(palette.color))document.body.setAttribute("data-md-color-"+key,value)}</script>





        <label class="md-header__button md-icon" for="__search">

          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>
        </label>
        <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">

        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.52 6.52 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5"/></svg>

        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">

          <a href="javascript:void(0)" class="md-search__icon md-icon" title="Share" aria-label="Share" data-clipboard data-clipboard-text="" data-md-component="search-share" tabindex="-1">

            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9a3 3 0 0 0-3 3 3 3 0 0 0 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.15c-.05.21-.08.43-.08.66 0 1.61 1.31 2.91 2.92 2.91s2.92-1.3 2.92-2.91A2.92 2.92 0 0 0 18 16.08"/></svg>
          </a>

        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">

          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>
        </button>
      </nav>

    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" tabindex="0" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>



      <div class="md-header__source">
        <!--
  Copyright (c) 2016-2019 Martin Donath <<EMAIL>>

  Permission is hereby granted, free of charge, to any person obtaining a copy
  of this software and associated documentation files (the "Software"), to
  deal in the Software without restriction, including without limitation the
  rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
  sell copies of the Software, and to permit persons to whom the Software is
  furnished to do so, subject to the following conditions:

  The above copyright notice and this permission notice shall be included in
  all copies or substantial portions of the Software.

  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL THE
  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
  IN THE SOFTWARE.
-->



<!--
  Check whether the repository is hosted on one of the supported code hosting
  platforms (GitHub, GitLab or Bitbucket) to show icon.
-->





<!-- Repository containing source -->
<a href="https://github.com/MsArkNet/webs" title="source.link.title"
    class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg viewBox="0 0 24 24" width="24" height="24">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>

  <div class="md-source__repository">
    MsArkNet/webs
  </div>
</a>
      </div>

  </nav>



<nav class="md-tabs" aria-label="Tabs" data-md-component="tabs">
  <div class="md-grid">
    <ul class="md-tabs__list">






    <li class="md-tabs__item">
      <a href="../.." class="md-tabs__link">





  Inicio

      </a>
    </li>










      <li class="md-tabs__item">
        <a href="../../infraestructura/" class="md-tabs__link">



  Infraestructura

        </a>
      </li>











      <li class="md-tabs__item">
        <a href="../../desarrollo/" class="md-tabs__link">



  Desarrollo

        </a>
      </li>













      <li class="md-tabs__item md-tabs__item--active">
        <a href="../" class="md-tabs__link">



  Sprints

        </a>
      </li>











      <li class="md-tabs__item">
        <a href="../../guias/" class="md-tabs__link">



  Guías

        </a>
      </li>




    </ul>
  </div>
</nav>


</header>

    <div class="md-container" data-md-component="container">




      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">



              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">






<nav class="md-nav md-nav--primary md-nav--lifted" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href="../.." title="MsArkNet SSO Documentation" class="md-nav__button md-logo" aria-label="MsArkNet SSO Documentation" data-md-component="logo">

  <img src="../../assets/logo.png" alt="logo">

    </a>
    MsArkNet SSO Documentation
  </label>

    <div class="md-nav__source">
      <!--
  Copyright (c) 2016-2019 Martin Donath <<EMAIL>>

  Permission is hereby granted, free of charge, to any person obtaining a copy
  of this software and associated documentation files (the "Software"), to
  deal in the Software without restriction, including without limitation the
  rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
  sell copies of the Software, and to permit persons to whom the Software is
  furnished to do so, subject to the following conditions:

  The above copyright notice and this permission notice shall be included in
  all copies or substantial portions of the Software.

  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL THE
  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
  IN THE SOFTWARE.
-->



<!--
  Check whether the repository is hosted on one of the supported code hosting
  platforms (GitHub, GitLab or Bitbucket) to show icon.
-->





<!-- Repository containing source -->
<a href="https://github.com/MsArkNet/webs" title="source.link.title"
    class="md-source" data-md-source="github">

    <div class="md-source__icon">
      <svg viewBox="0 0 24 24" width="24" height="24">
        <use xlink:href="#__github" width="24" height="24"></use>
      </svg>
    </div>

  <div class="md-source__repository">
    MsArkNet/webs
  </div>
</a>
    </div>

  <ul class="md-nav__list" data-md-scrollfix>







    <li class="md-nav__item">
      <a href="../.." class="md-nav__link">



  <span class="md-ellipsis">
    Inicio

  </span>


      </a>
    </li>



















    <li class="md-nav__item md-nav__item--nested">





        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_2" >


          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="0">



  <span class="md-ellipsis">
    Infraestructura

  </span>


            <span class="md-nav__icon md-icon"></span>
          </label>

        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            Infraestructura
          </label>
          <ul class="md-nav__list" data-md-scrollfix>







    <li class="md-nav__item">
      <a href="../../infraestructura/" class="md-nav__link">



  <span class="md-ellipsis">
    MsArkNet - Entorno Docker con Traefik

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="../../infraestructura/traefik-setup/" class="md-nav__link">



  <span class="md-ellipsis">
    Configuración Traefik

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="../../infraestructura/ssl-certificates/" class="md-nav__link">



  <span class="md-ellipsis">
    Certificados SSL

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="../../infraestructura/troubleshooting/" class="md-nav__link">



  <span class="md-ellipsis">
    Troubleshooting

  </span>


      </a>
    </li>




          </ul>
        </nav>

    </li>



















    <li class="md-nav__item md-nav__item--nested">





        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_3" >


          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="0">



  <span class="md-ellipsis">
    Desarrollo

  </span>


            <span class="md-nav__icon md-icon"></span>
          </label>

        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Desarrollo
          </label>
          <ul class="md-nav__list" data-md-scrollfix>







    <li class="md-nav__item">
      <a href="../../desarrollo/" class="md-nav__link">



  <span class="md-ellipsis">
    Desarrollo

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="../../desarrollo/checklist-implementacion/" class="md-nav__link">



  <span class="md-ellipsis">
    Checklist Implementación

  </span>


      </a>
    </li>




          </ul>
        </nav>

    </li>
























    <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">



        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_4" checked>


          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="">



  <span class="md-ellipsis">
    Sprints

  </span>


            <span class="md-nav__icon md-icon"></span>
          </label>

        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Sprints
          </label>
          <ul class="md-nav__list" data-md-scrollfix>







    <li class="md-nav__item">
      <a href="../" class="md-nav__link">



  <span class="md-ellipsis">
    Plan de Sprints

  </span>


      </a>
    </li>












    <li class="md-nav__item md-nav__item--active">

      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">





        <label class="md-nav__link md-nav__link--active" for="__toc">



  <span class="md-ellipsis">
    Plan de Sprints

  </span>


          <span class="md-nav__icon md-icon"></span>
        </label>

      <a href="./" class="md-nav__link md-nav__link--active">



  <span class="md-ellipsis">
    Plan de Sprints

  </span>


      </a>


<nav class="md-nav md-nav--secondary">





    <!--<label class="md-nav__title" for="__toc">toc.title</label>-->
    <ul class="md-nav__list" data-md-scrollfix>

        <li class="md-nav__item">
  <a href="#estructura-de-repos-sugerida" class="md-nav__link">
    <span class="md-ellipsis">
      Estructura de repos (sugerida)
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#sprint-0-fundaciones-infra" class="md-nav__link">
    <span class="md-ellipsis">
      Sprint 0 — Fundaciones &amp; Infra
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#sprint-1-oidc-discovery-jwks-authorization-endpoint-pkce" class="md-nav__link">
    <span class="md-ellipsis">
      Sprint 1 — OIDC Discovery + JWKS + Authorization Endpoint (PKCE)
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#sprint-2-token-endpoint-emision-jwt-idaccess-middleware-jwt" class="md-nav__link">
    <span class="md-ellipsis">
      Sprint 2 — Token Endpoint + Emisión JWT (ID/Access) + Middleware JWT
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#sprint-3-sso-logout-gestion-de-sesiones-fe" class="md-nav__link">
    <span class="md-ellipsis">
      Sprint 3 — SSO Logout + Gestión de Sesiones (FE)
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#sprint-4-rbacpermissions-consent-real-por-cliente" class="md-nav__link">
    <span class="md-ellipsis">
      Sprint 4 — RBAC/Permissions + Consent real por cliente
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#sprint-5-observabilidad-seguridad-operacional" class="md-nav__link">
    <span class="md-ellipsis">
      Sprint 5 — Observabilidad &amp; Seguridad Operacional
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#sprint-6-rotacion-de-claves-kmshsm-nivel-pro-docs" class="md-nav__link">
    <span class="md-ellipsis">
      Sprint 6 — Rotación de Claves + KMS/HSM (nivel pro) + Docs
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#detalle-funcional-por-checklist-trazabilidad" class="md-nav__link">
    <span class="md-ellipsis">
      Detalle funcional por checklist (trazabilidad)
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#historias-por-sprint-resumen" class="md-nav__link">
    <span class="md-ellipsis">
      Historias por sprint (resumen)
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#backlog-tecnico-crea-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Backlog técnico (crea issues)
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#variables-de-entorno-borrador-env" class="md-nav__link">
    <span class="md-ellipsis">
      Variables de entorno (borrador .env)
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#criterios-de-calidad-globales" class="md-nav__link">
    <span class="md-ellipsis">
      Criterios de calidad (globales)
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#proximos-pasos" class="md-nav__link">
    <span class="md-ellipsis">
      Próximos pasos
    </span>
  </a>

</li>





    </ul>

</nav>

    </li>




          </ul>
        </nav>

    </li>



















    <li class="md-nav__item md-nav__item--nested">





        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_5" >


          <label class="md-nav__link" for="__nav_5" id="__nav_5_label" tabindex="0">



  <span class="md-ellipsis">
    Guías

  </span>


            <span class="md-nav__icon md-icon"></span>
          </label>

        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_5_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_5">
            <span class="md-nav__icon md-icon"></span>
            Guías
          </label>
          <ul class="md-nav__list" data-md-scrollfix>







    <li class="md-nav__item">
      <a href="../../guias/" class="md-nav__link">



  <span class="md-ellipsis">
    Guías

  </span>


      </a>
    </li>










    <li class="md-nav__item">
      <a href="../../guias/instalaciones/" class="md-nav__link">



  <span class="md-ellipsis">
    Instalaciones

  </span>


      </a>
    </li>




          </ul>
        </nav>

    </li>



  </ul>
</nav>
                  </div>
                </div>
              </div>



              <div class="md-sidebar md-sidebar--secondary" data-md-component="sidebar" data-md-type="toc" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">

<nav class="md-nav md-nav--secondary">





    <!--<label class="md-nav__title" for="__toc">toc.title</label>-->
    <ul class="md-nav__list" data-md-scrollfix>

        <li class="md-nav__item">
  <a href="#estructura-de-repos-sugerida" class="md-nav__link">
    <span class="md-ellipsis">
      Estructura de repos (sugerida)
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#sprint-0-fundaciones-infra" class="md-nav__link">
    <span class="md-ellipsis">
      Sprint 0 — Fundaciones &amp; Infra
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#sprint-1-oidc-discovery-jwks-authorization-endpoint-pkce" class="md-nav__link">
    <span class="md-ellipsis">
      Sprint 1 — OIDC Discovery + JWKS + Authorization Endpoint (PKCE)
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#sprint-2-token-endpoint-emision-jwt-idaccess-middleware-jwt" class="md-nav__link">
    <span class="md-ellipsis">
      Sprint 2 — Token Endpoint + Emisión JWT (ID/Access) + Middleware JWT
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#sprint-3-sso-logout-gestion-de-sesiones-fe" class="md-nav__link">
    <span class="md-ellipsis">
      Sprint 3 — SSO Logout + Gestión de Sesiones (FE)
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#sprint-4-rbacpermissions-consent-real-por-cliente" class="md-nav__link">
    <span class="md-ellipsis">
      Sprint 4 — RBAC/Permissions + Consent real por cliente
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#sprint-5-observabilidad-seguridad-operacional" class="md-nav__link">
    <span class="md-ellipsis">
      Sprint 5 — Observabilidad &amp; Seguridad Operacional
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#sprint-6-rotacion-de-claves-kmshsm-nivel-pro-docs" class="md-nav__link">
    <span class="md-ellipsis">
      Sprint 6 — Rotación de Claves + KMS/HSM (nivel pro) + Docs
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#detalle-funcional-por-checklist-trazabilidad" class="md-nav__link">
    <span class="md-ellipsis">
      Detalle funcional por checklist (trazabilidad)
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#historias-por-sprint-resumen" class="md-nav__link">
    <span class="md-ellipsis">
      Historias por sprint (resumen)
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#backlog-tecnico-crea-issues" class="md-nav__link">
    <span class="md-ellipsis">
      Backlog técnico (crea issues)
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#variables-de-entorno-borrador-env" class="md-nav__link">
    <span class="md-ellipsis">
      Variables de entorno (borrador .env)
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#criterios-de-calidad-globales" class="md-nav__link">
    <span class="md-ellipsis">
      Criterios de calidad (globales)
    </span>
  </a>

</li>

        <li class="md-nav__item">
  <a href="#proximos-pasos" class="md-nav__link">
    <span class="md-ellipsis">
      Próximos pasos
    </span>
  </a>

</li>





    </ul>

</nav>
                  </div>
                </div>
              </div>



            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">








<h1 id="plan-de-sprints-sso-cerebro-fe-be-en-paralelo">Plan de Sprints — SSO &ldquo;Cerebro&rdquo; (FE + BE en paralelo)<a class="headerlink" href="#plan-de-sprints-sso-cerebro-fe-be-en-paralelo" title="Permanent link"></a></h1>
<blockquote>
<p>Objetivo: entregar un <strong>IdP OIDC</strong> (Cerebro) + <strong>panel frontend</strong> (cerebro‑fe) + <strong>SDK/middleware</strong> para -be clientes (abogados‑be, bitcoin‑be) con <strong>Auth Code + PKCE</strong>, <strong>SSO + back‑channel logout</strong>, <strong>RBAC/consent</strong>, <strong>observabilidad</strong>, <strong>rotación de claves</strong> y <strong>Swagger</strong> para pruebas.</p>
</blockquote>
<hr />
<h2 id="estructura-de-repos-sugerida">Estructura de repos (sugerida)<a class="headerlink" href="#estructura-de-repos-sugerida" title="Permanent link"></a></h2>
<ul>
<li><strong>apps/cerebro-idp</strong> (NestJS/Express + Prisma) — Proveedor OIDC.</li>
<li><strong>apps/cerebro-fe</strong> (React + Vite + TS) — Panel de sesiones, dispositivos, consent, admin RBAC.</li>
<li><strong>apps/abogados-be</strong> y <strong>apps/bitcoin-be</strong> — Resource servers de ejemplo (Swagger + middleware JWT).</li>
<li><strong>packages/auth-mw</strong> — Middleware/SDK validación JWT + cache JWKS (Node/Express/Nest).</li>
<li><strong>infra/</strong> — Docker Compose (MariaDB, Redis, Traefik + Let&rsquo;s Encrypt, OTEL Collector, Prom/Grafana/Loki), seeders, migraciones.</li>
</ul>
<hr />
<h2 id="sprint-0-fundaciones-infra">Sprint 0 — Fundaciones &amp; Infra<a class="headerlink" href="#sprint-0-fundaciones-infra" title="Permanent link"></a></h2>
<p><strong>Objetivo:</strong> Entorno reproducible y baseline de calidad.</p>
<p><strong>Entregables</strong></p>
<ul>
<li><del>Docker Compose con <strong>MariaDB</strong>, <strong>Redis</strong>, <strong>Traefik</strong> (TLS Let’s Encrypt), <strong>OTel Collector</strong>, <strong>Prometheus</strong>, <strong>Grafana</strong>, <strong>Loki</strong>.</del></li>
<li>Repos iniciales + CI (lint/test/build) + quality gates.</li>
<li><strong>Prisma</strong> conectado a MariaDB, migración inicial + <strong>seed oauth_clients</strong> (bitcoin, abogados, cerebro‑fe).</li>
<li>Estructura de <strong>.env.example</strong> (secretos por servicio) + guía de secretos.</li>
<li>Swagger básico vivo en cada -be (hello + /health).</li>
</ul>
<p><strong>Tareas</strong></p>
<ul>
<li>Scaffold repos + monorepo (pnpm/lerna/turbo) o multi‑repo (a elegir).</li>
<li>Traefik reglas + certificados LE wildcard.</li>
<li>Prisma schema (users, sessions, oauth_clients, oauth_codes, tokens, jwk_keys, consents, roles, permissions, audit_logs).</li>
<li>Seed inicial de clientes con redirect_uris y backchannel_logout_uri.</li>
<li>Telemetry bootstrap (OTel SDK), structured logging (pino) y correlation-id.</li>
</ul>
<p><strong>DoD</strong></p>
<ul>
<li><code>docker compose up</code> levanta todo con TLS.</li>
<li><code>prisma migrate deploy</code> + <code>prisma db seed</code> OK.</li>
<li><code>/health</code> y <code>/metrics</code> accesibles por cada servicio.</li>
</ul>
<hr />
<h2 id="sprint-1-oidc-discovery-jwks-authorization-endpoint-pkce">Sprint 1 — OIDC Discovery + JWKS + Authorization Endpoint (PKCE)<a class="headerlink" href="#sprint-1-oidc-discovery-jwks-authorization-endpoint-pkce" title="Permanent link"></a></h2>
<p><strong>Objetivo:</strong> Implementar núcleo OIDC publicable.</p>
<p><strong>Entregables</strong></p>
<ul>
<li><code>/.well-known/openid-configuration</code> completo.</li>
<li><code>/jwks.json</code> con <strong>kid</strong> y storage en DB (tabla <code>jwk_keys</code>).</li>
<li><code>/authorize</code> con <strong>Authorization Code + PKCE</strong> (S256), consent mínimo stub.</li>
<li>UI cerebro‑fe: <strong>Login</strong>, <strong>Selector de cliente</strong>, <strong>pantalla de Consent (stub)</strong>.</li>
</ul>
<p><strong>Tareas</strong></p>
<ul>
<li>Generación/rotación manual de par de claves (RSA/ECDSA) y persistencia.</li>
<li>Validaciones PKCE (code_verifier, code_challenge).</li>
<li>Persistencia de <code>authorization_code</code> (tabla <code>oauth_codes</code>).</li>
<li>UI: formularios, estado, redirecciones con <code>state</code> y <code>nonce</code>.</li>
<li>Tests integración: flujos <code>/authorize</code> happy path + errores (missing redirect_uri, invalid_client, invalid_scope, pkce_invalid).</li>
</ul>
<p><strong>DoD</strong></p>
<ul>
<li>OpenID Provider Metadata válida en OIDC playground.</li>
<li>Se emite <code>code</code> y redirige correctamente al <code>redirect_uri</code> del cliente.</li>
</ul>
<hr />
<h2 id="sprint-2-token-endpoint-emision-jwt-idaccess-middleware-jwt">Sprint 2 — Token Endpoint + Emisión JWT (ID/Access) + Middleware JWT<a class="headerlink" href="#sprint-2-token-endpoint-emision-jwt-idaccess-middleware-jwt" title="Permanent link"></a></h2>
<p><strong>Objetivo:</strong> Completar Auth Code Flow end‑to‑end y consumo por APIs.</p>
<p><strong>Entregables</strong></p>
<ul>
<li><code>/token</code> con <code>authorization_code</code> + <strong>PKCE</strong> y <strong>refresh_token</strong> (rotación activada).</li>
<li>Emisión <strong>ID Token</strong> y <strong>Access Token JWT</strong> con <code>kid</code>, <code>aud</code> por cliente, <code>scope</code>/permissions y <code>exp</code>.</li>
<li><strong>packages/auth-mw</strong>: middleware de validación JWT (firma, <code>aud</code>, <code>exp</code>, <code>nbf</code>) + <strong>cache JWKS</strong> con TTL + fallback.</li>
<li>Swagger en abogados‑be/bitcoin‑be con <strong>OAuth2 Auth Code</strong> para probar.</li>
</ul>
<p><strong>Tareas</strong></p>
<ul>
<li>Claims: <code>iss</code>, <code>sub</code>, <code>aud</code>, <code>azp</code>, <code>iat</code>, <code>exp</code>, <code>auth_time</code>, <code>amr</code>, <code>nonce</code> (si aplica), <code>scope</code>, <code>roles</code> (si ya disponibles), <code>sid</code>.</li>
<li>Refresh token rotation + revocación en DB (tabla <code>tokens</code>).</li>
<li>UI: flujo completo login → consent → redirect al cliente demo.</li>
<li>Tests: firma JWS, invalid aud, token expirado, JWKS cache miss/hit.</li>
</ul>
<p><strong>DoD</strong></p>
<ul>
<li>Cliente demo llama a abogados‑be/bitcoin‑be con <code>Authorization: Bearer</code> y pasa middleware.</li>
</ul>
<hr />
<h2 id="sprint-3-sso-logout-gestion-de-sesiones-fe">Sprint 3 — SSO Logout + Gestión de Sesiones (FE)<a class="headerlink" href="#sprint-3-sso-logout-gestion-de-sesiones-fe" title="Permanent link"></a></h2>
<p><strong>Objetivo:</strong> Cerrar sesiones globalmente y visibilidad al usuario.</p>
<p><strong>Entregables</strong></p>
<ul>
<li><strong>Front‑channel y back‑channel logout</strong>: notificación a <code>backchannel_logout_uri</code> y limpieza de <code>sid</code>.</li>
<li>Panel <strong>cerebro‑fe</strong>: sesiones activas, dispositivos, cerrar sesión por dispositivo, revocar refresh tokens.</li>
<li>Endpoint <code>/revocation</code> (RFC 7009) y <code>/introspect</code> (opcional para debug).</li>
</ul>
<p><strong>Tareas</strong></p>
<ul>
<li>Modelo de <strong>session store en Redis</strong> (sid ↔ user ↔ clients ↔ tokens).</li>
<li>Auditoría de eventos: login, token_issued, refresh_rotated, logout, revoke.</li>
<li>UI: tabla de sesiones, device fingerprint básico, acciones.</li>
<li>Tests e2e (Playwright): login multi‑cliente, logout global, back‑channel recibido por clientes demo.</li>
</ul>
<p><strong>DoD</strong></p>
<ul>
<li>Al hacer logout global, clientes reciben back‑channel y niegan tokens siguientes.</li>
</ul>
<hr />
<h2 id="sprint-4-rbacpermissions-consent-real-por-cliente">Sprint 4 — RBAC/Permissions + Consent real por cliente<a class="headerlink" href="#sprint-4-rbacpermissions-consent-real-por-cliente" title="Permanent link"></a></h2>
<p><strong>Objetivo:</strong> Control fino de acceso y consentimiento configurable.</p>
<p><strong>Entregables</strong></p>
<ul>
<li>Modelo <strong>RBAC</strong>: <code>roles</code>, <code>permissions</code>, <code>role_permissions</code>, <code>user_roles</code> por <strong>cliente</strong>.</li>
<li><strong>Consent UI</strong>: scopes/grants por cliente; recordar decisión.</li>
<li>Mapeo de <strong>roles/permissions → claims</strong> en Access/ID token.</li>
<li>Panel admin en <strong>cerebro‑fe</strong>: asignar roles a usuarios, ver permisos por cliente.</li>
</ul>
<p><strong>Tareas</strong></p>
<ul>
<li>Migraciones Prisma + seed roles/permisos base.</li>
<li>Política de scopes (<code>openid email profile offline_access …</code> + custom <code>abogados:read</code>, <code>bitcoin:tx:*</code>).</li>
<li>Filtros por <code>aud</code> y <code>scope</code> en middleware.</li>
<li>Tests: cambio de rol refleja claims; denegación por falta de permiso.</li>
</ul>
<p><strong>DoD</strong></p>
<ul>
<li>Un usuario sin permiso no puede acceder a endpoint protegido aunque tenga token válido.</li>
</ul>
<hr />
<h2 id="sprint-5-observabilidad-seguridad-operacional">Sprint 5 — Observabilidad &amp; Seguridad Operacional<a class="headerlink" href="#sprint-5-observabilidad-seguridad-operacional" title="Permanent link"></a></h2>
<p><strong>Objetivo:</strong> Visibilidad total + límites de abuso.</p>
<p><strong>Entregables</strong></p>
<ul>
<li><strong>Audit logs</strong> consultables en Grafana/Loki.</li>
<li><strong>Métricas</strong> (Prometheus): tasas de login, errores por tipo, latencias, 95/99p; cardinalidad controlada.</li>
<li><strong>Traces</strong> OTel end‑to‑end (idp ↔ clientes) con baggage <code>user_id</code> (anonimizado) y <code>request_id</code>.</li>
<li><strong>Rate limiting</strong> por IP/cliente/usuario para <code>/authorize</code>, <code>/token</code>, <code>/jwks</code>, <code>/introspect</code>, <code>/revocation</code>.</li>
</ul>
<p><strong>Tareas</strong></p>
<ul>
<li>Exporters OTel → Collector → Prom/Grafana.</li>
<li>Dashboards: salud OIDC, picos de error, top clientes.</li>
<li>Límites por Redis (token bucket) + respuestas RFC (429 con <code>Retry-After</code>).</li>
<li>Hardening: headers de seguridad, CSP en FE, cookie flags, SameSite.</li>
</ul>
<p><strong>DoD</strong></p>
<ul>
<li>Tableros listos + alertas básicas (p99 &gt; umbral, 5xx burst, fallo LE renew).</li>
</ul>
<hr />
<h2 id="sprint-6-rotacion-de-claves-kmshsm-nivel-pro-docs">Sprint 6 — Rotación de Claves + KMS/HSM (nivel pro) + Docs<a class="headerlink" href="#sprint-6-rotacion-de-claves-kmshsm-nivel-pro-docs" title="Permanent link"></a></h2>
<p><strong>Objetivo:</strong> Seguridad criptográfica y operativa a largo plazo.</p>
<p><strong>Entregables</strong></p>
<ul>
<li>Política de <strong>rotación de claves</strong> (programada) y retiro seguro de claves antiguas.</li>
<li>Integración opcional con <strong>KMS/HSM</strong> (abstracción de firma JWS).</li>
<li><strong>Runbooks</strong> y <strong>Swagger</strong> completo (con ejemplos por flujo) + guía de integración para clientes.</li>
</ul>
<p><strong>Tareas</strong></p>
<ul>
<li>Tabla <code>jwk_keys</code>: estados (active, retired), <code>kid</code> inmutable, ventana de solape JWKS.</li>
<li>Job de rotación (cron) + pruebas de rollover (old kid aún en JWKS).</li>
<li>Abstracción de firma para usar KMS/HSM o key local.</li>
<li>Documentación: flujos, secuencias, errores, mapping claims, ejemplos cURL/Postman.</li>
</ul>
<p><strong>DoD</strong></p>
<ul>
<li>Rollover sin downtime: tokens nuevos con <code>kid</code> nuevo, validación legacy operativa.</li>
</ul>
<hr />
<h2 id="detalle-funcional-por-checklist-trazabilidad">Detalle funcional por checklist (trazabilidad)<a class="headerlink" href="#detalle-funcional-por-checklist-trazabilidad" title="Permanent link"></a></h2>
<ul>
<li><strong>Infra</strong>: MariaDB, Redis, dominios/TLS LE, secretos .env → <em>Sprint 0</em>.</li>
<li><strong>DB + Prisma + seed oauth_clients</strong> → <em>Sprints 0–1</em>.</li>
<li><strong>OIDC Discovery + JWKS</strong> → <em>Sprint 1</em>.</li>
<li><strong>Auth Code + PKCE + tokens/rotation/revocation</strong> → <em>Sprints 1–3</em>.</li>
<li><strong>JWT con kid/aud/scope</strong> → <em>Sprint 2</em>.</li>
<li><strong>Middleware JWT + cache JWKS</strong> → <em>Sprint 2</em>.</li>
<li><strong>Logout SSO + back‑channel</strong> → <em>Sprint 3</em>.</li>
<li><strong>Gestión sesiones (FE)</strong> → <em>Sprint 3</em>.</li>
<li><strong>RBAC/permissions + consent</strong> → <em>Sprint 4</em>.</li>
<li><strong>Observabilidad (audit, métricas, traces) + rate limiting</strong> → <em>Sprint 5</em>.</li>
<li><strong>Rotación de claves (KMS/HSM)</strong> → <em>Sprint 6</em>.</li>
<li><strong>Swagger UI</strong> → <em>Sprints 0–6 (in crescendo)</em>.</li>
</ul>
<hr />
<h2 id="historias-por-sprint-resumen">Historias por sprint (resumen)<a class="headerlink" href="#historias-por-sprint-resumen" title="Permanent link"></a></h2>
<p><strong>S0</strong></p>
<ul>
<li>Como dev, quiero levantar todo con <code>docker compose</code> y TLS.</li>
<li>Como dev, quiero migrar y seedear DB en un comando.</li>
</ul>
<p><strong>S1</strong></p>
<ul>
<li>Como cliente, quiero descubrir capacidades OIDC (<code>/.well-known</code>).</li>
<li>Como usuario, quiero iniciar sesión y obtener un <code>code</code> con PKCE.</li>
</ul>
<p><strong>S2</strong></p>
<ul>
<li>Como cliente, quiero canjear <code>code</code> por tokens (ID/Access/Refresh).</li>
<li>Como API, quiero validar JWT con JWKS cacheado.</li>
</ul>
<p><strong>S3</strong></p>
<ul>
<li>Como usuario, quiero ver/cerrar mis sesiones y dispositivos.</li>
<li>Como cliente, quiero recibir back‑channel logout y anular <code>sid</code>.</li>
</ul>
<p><strong>S4</strong></p>
<ul>
<li>Como admin, quiero asignar roles/permissions por cliente.</li>
<li>Como usuario, quiero aceptar/rechazar scopes por cliente.</li>
</ul>
<p><strong>S5</strong></p>
<ul>
<li>Como SRE, quiero métricas/traces/logs con alertas.</li>
<li>Como plataforma, quiero limitar abusos con rate limiting.</li>
</ul>
<p><strong>S6</strong></p>
<ul>
<li>Como seguridad, quiero rotar claves sin impacto.</li>
<li>Como integrador, quiero docs/Swagger y ejemplos listos.</li>
</ul>
<hr />
<h2 id="backlog-tecnico-crea-issues">Backlog técnico (crea issues)<a class="headerlink" href="#backlog-tecnico-crea-issues" title="Permanent link"></a></h2>
<ul>
<li>Plantillas de <strong>.env.example</strong> (todos los servicios) + <code>scripts/check-env.ts</code>.</li>
<li>Comandos NPM: <code>dev</code>, <code>build</code>, <code>start</code>, <code>migrate</code>, <code>seed</code>, <code>lint</code>, <code>test</code>, <code>e2e</code>.</li>
<li><strong>SDK auth-mw</strong> exportando: expressMiddleware(), nestGuard(), jwksClient(cache), verifyJwt(aud,scope), requireScope().</li>
<li><strong>Playwright</strong> e2e: login, consent, token, refresh‑rotation, logout back‑channel.</li>
<li>Dashboards Grafana (OIDC, Redis, DB).</li>
<li>Scripts de <strong>data retention</strong> (tokens y logs antiguos).</li>
</ul>
<hr />
<h2 id="variables-de-entorno-borrador-env">Variables de entorno (borrador .env)<a class="headerlink" href="#variables-de-entorno-borrador-env" title="Permanent link"></a></h2>
<ul>
<li><code>CEREBRO_ISSUER=https://auth.tu-dominio.com</code></li>
<li><code>CEREBRO_JWKS_ALG=RS256</code> (o ES256)</li>
<li><code>DATABASE_URL=mariadb://user:pass@maria:3306/cerebro</code></li>
<li><code>REDIS_URL=redis://redis:6379/0</code></li>
<li><code>COOKIE_SECURE=true</code> <code>COOKIE_SAMESITE=Lax</code></li>
<li><code>TRUST_PROXY=1</code></li>
<li><code>OTEL_EXPORTER_OTLP_ENDPOINT=http://otel-collector:4317</code></li>
<li><code>RATE_LIMIT_AUTHZ=10/m</code> <code>RATE_LIMIT_TOKEN=20/m</code></li>
</ul>
<hr />
<h2 id="criterios-de-calidad-globales">Criterios de calidad (globales)<a class="headerlink" href="#criterios-de-calidad-globales" title="Permanent link"></a></h2>
<ul>
<li><strong>Seguridad</strong>: PKCE S256 obligatorio, SameSite+Secure, CSRF en FE, scopes mínimos, refresh rotation, back‑channel implementado, RBAC por cliente.</li>
<li><strong>Confiabilidad</strong>: pruebas unitarias + integración + e2e; rate limiting; circuit breakers para JWKS.</li>
<li><strong>Observabilidad</strong>: logs estructurados, métricas y traces con correlación.</li>
<li><strong>DX</strong>: Swagger vivo, ejemplos cURL, scripts de arranque y seeds.</li>
<li><strong>Docs</strong>: README por servicio + runbooks.</li>
</ul>
<hr />
<h2 id="proximos-pasos">Próximos pasos<a class="headerlink" href="#proximos-pasos" title="Permanent link"></a></h2>
<ol>
<li>Confirmar stack exacto (NestJS vs Express, monorepo vs multi‑repo).</li>
<li>Crear repos + <code>infra/</code> (Sprint 0) y abrir issues del Sprint 0.</li>
<li>Conectar tu <strong>base de React Vite</strong> a <code>cerebro-idp</code> (login/consent) desde S1.</li>
</ol>













              </article>
            </div>


<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>

          <button type="button" class="md-top md-icon" data-md-component="top" hidden>

  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M13 20h-2V8l-5.5 5.5-1.42-1.42L12 4.16l7.92 7.92-1.42 1.42L13 8z"/></svg>
  Back to top
</button>

      </main>

        <!--
  Copyright (c) 2016-2018 Martin Donath <<EMAIL>>

  Permission is hereby granted, free of charge, to any person obtaining a copy
  of this software and associated documentation files (the "Software"), to
  deal in the Software without restriction, including without limitation the
  rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
  sell copies of the Software, and to permit persons to whom the Software is
  furnished to do so, subject to the following conditions:

  The above copyright notice and this permission notice shall be included in
  all copies or substantial portions of the Software.

  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL THE
  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
  IN THE SOFTWARE.
-->



<!-- Application footer -->
<footer class="md-footer">

  <!-- Further information -->
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">

      <!-- Copyright and theme information -->
      <div class="md-footer-copyright" style="padding-top: 0px; padding-bottom: 0px;">

          <div class="md-footer-copyright__highlight">
            MsArkNet is licensed under the MIT License (MIT), this <a href="https://readthedocs.org/">Readthedocs.org</a> documentation uses <a href="https://www.mkdocs.org/">Mkdocs</a> and the <a href="https://squidfunk.github.io/mkdocs-material">Material theme</a>.
          </div>

      </div>

      <!-- Social links -->


<div class="md-social">

</div>

    </div>
  </div>
</footer>

    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>




      <script id="__config" type="application/json">{"base": "../..", "features": ["navigation.tabs", "navigation.tabs.sticky", "navigation.sections", "navigation.expand", "navigation.path", "navigation.top", "search.highlight", "search.share", "content.code.copy", "content.code.annotate"], "search": "../../assets/javascripts/workers/search.973d3a69.min.js", "tags": null, "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}, "version": {"provider": "mike"}}</script>


      <script src="../../assets/javascripts/bundle.f55a23d4.min.js"></script>


  </body>
</html>