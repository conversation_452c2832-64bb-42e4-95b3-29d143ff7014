{"config": {"lang": ["en"], "separator": "[\\s\\-]+", "pipeline": ["stop<PERSON>ordFilter"]}, "docs": [{"location": "", "title": "MsArkNet SSO - Documentación", "text": "<p>Bienvenido a la documentación completa del sistema SSO (Single Sign-On) de MsArkNet. Esta documentación está organizada por proyectos y contiene toda la información necesaria para desarrollar, configurar y mantener el sistema.</p>"}, {"location": "#que-es-MsArkNet-sso", "title": "🎯 ¿Qué es MsArkNet SSO?", "text": "<p>MsArkNet SSO es un sistema de autenticación centralizada basado en OpenID Connect (OIDC) que permite a los usuarios acceder a múltiples aplicaciones con una sola cuenta. El sistema está diseñado para ser:</p> <ul> <li>Seguro: Implementa Authorization Code + PKCE</li> <li>Escalable: Arquitectura basada en microservicios</li> <li>Observable: Métricas, logs y trazas completas</li> <li>Moderno: Stack tecnológico actualizado</li> </ul>"}, {"location": "#estructura-de-la-documentacion", "title": "📚 Estructura de la Documentación", "text": ""}, {"location": "#infraestructura", "title": "🏗️ Infraestructura", "text": "<p>Configuración del entorno de desarrollo con Dock<PERSON>, Traefik, certificados SSL y servicios de monitorización.</p> <ul> <li>Configuración de Traefik como proxy reverso</li> <li>Gestión de certificados SSL/TLS</li> <li>Servicios de desarrollo (Grafana, Prometheus)</li> <li>Troubleshooting y debugging</li> </ul>"}, {"location": "#desarrollo", "title": "💻 Desarrollo", "text": "<p>Guías y documentación técnica para el desarrollo del sistema SSO.</p> <ul> <li>Checklist de implementación paso a paso</li> <li>Arquitectura del sistema</li> <li>Flujos de autenticación</li> <li>Tecnologías utilizadas</li> </ul>"}, {"location": "#sprints", "title": "📅 Sprints", "text": "<p>Planificación y seguimiento del desarrollo organizado en sprints.</p> <ul> <li>Plan detallado de sprints</li> <li>Objetivos y entregables</li> <li>Estado actual del proyecto</li> <li>Estructura de repositorios</li> </ul>"}, {"location": "#guias", "title": "📖 Guías", "text": "<p>Tutoriales prácticos y guías de instalación.</p> <ul> <li>Instalación de herramientas</li> <li>Configuración de entorno</li> <li>Comandos útiles</li> <li>Recursos adicionales</li> </ul>"}, {"location": "#inicio-rapido", "title": "🚀 <PERSON><PERSON><PERSON>", "text": "<ol> <li>Configurar infraestructura: Sigue la guía de infraestructura</li> <li>Revisar el checklist: Consulta el checklist de implementación</li> <li>Planificar desarrollo: Revisa el plan de sprints</li> <li>Instalar herramientas: Sigue las guías de instalación</li> </ol>"}, {"location": "#stack-tecnologico", "title": "🛠️ Stack Te<PERSON>nológico", "text": "<ul> <li>Backend: NestJS/Express + Prisma</li> <li>Frontend: React + Vite + TypeScript</li> <li>Base de datos: MariaDB</li> <li>Cache: Redis</li> <li>Proxy: Traefik</li> <li>Contenedores: Docker + Docker Compose</li> <li>Observabilidad: OpenTelemetry + Prometheus + Grafana</li> </ul>"}, {"location": "#enlaces-utiles", "title": "🔗 Enlaces Útiles", "text": "<ul> <li>Repositorio del proyecto</li> <li>Discord de la comunidad</li> <li>Sitio web oficial</li> </ul>"}, {"location": "desarrollo/", "title": "Desarrollo", "text": "<p>Esta sección contiene documentación relacionada con el desarrollo del proyecto MsArkNet SSO.</p>"}, {"location": "desarrollo/#documentos-disponibles", "title": "📋 Documentos Disponibles", "text": ""}, {"location": "desarrollo/#checklist-de-implementacion", "title": "Checklist de Implementación", "text": "<p>Lista de tareas paso a paso para implementar el sistema SSO completo, incluyendo:</p> <ul> <li>Infraestructura (MariaDB, Redis, dominios y TLS)</li> <li>Esquema de base de datos y migraciones</li> <li>Implementación OIDC Discovery + JWKS</li> <li>Flujo Authorization Code + PKCE</li> <li>Gestión de JWT y tokens</li> <li>Middleware de validación</li> <li>Sistema de logout SSO</li> <li>RBAC y permisos</li> <li>Observabilidad y métricas</li> </ul>"}, {"location": "desarrollo/#arquitectura-del-sistema", "title": "Arquitectura del Sistema", "text": "<p>El sistema SSO está diseñado con los siguientes componentes principales:</p> <ul> <li>Cerebro IdP: Proveedor de identidad OIDC</li> <li>Cerebro FE: Panel frontend para gestión de sesiones</li> <li>Resource Servers: Servicios backend (abogados-be, bitcoin-be)</li> <li>Auth Middleware: SDK para validación JWT</li> </ul>"}, {"location": "desarrollo/#tecnologias-utilizadas", "title": "Tecnologías Utilizadas", "text": "<ul> <li>Backend: NestJS/Express + Prisma</li> <li>Frontend: React + Vite + TypeScript</li> <li>Base de datos: MariaDB</li> <li>Cache: Redis</li> <li>Proxy: Traefik</li> <li>Contenedores: Docker + Docker Compose</li> <li>Observabilidad: OpenTelemetry + Prometheus + Grafana</li> </ul>"}, {"location": "desarrollo/#flujo-de-autenticacion", "title": "Flujo de Autenticación", "text": "<ol> <li>Authorization Code + PKCE: Flujo seguro para SPAs</li> <li>JWT Tokens: Con kid y aud por cliente</li> <li>Scope/Permissions: Sistema granular de permisos</li> <li>Session Management: Gestión centralizada de sesiones</li> <li>Back-channel Logout: Logout distribuido</li> </ol>"}, {"location": "desarrollo/#proximos-pasos", "title": "Próximos Pasos", "text": "<p>Consulta el Plan de Sprints para ver la hoja de ruta detallada del desarrollo.</p>"}, {"location": "desarrollo/checklist-implementacion/", "title": "Checklist Implementación", "text": "<p>Checklist de implementación (paso a paso)</p> <ul> <li>Infra: MariaDB, Redis, dominios y TLS (Let’s Encrypt), secretos en .env.</li> <li>Esquema DB (Prisma migrations) y seed de oauth_clients (bitcoin, abogados, cerebro-fe).</li> <li>OIDC Discovery + JWKS en Cerebro.</li> <li>Flujo Authorization Code + PKCE completo (codes, tokens, rotation, revocation).</li> <li>Emisión JWT con kid y aud por cliente; scope/permissions.</li> <li>Middleware validación JWT en cada -be + cache JWKS.</li> <li>Logout SSO + back-channel.</li> <li>Gestión sesiones (panel en cerebro-fe): cerrar sesiones, ver dispositivos.</li> <li>RBAC/permissions y “consent” por cliente (opcional).</li> <li>Observabilidad: audit logs, métricas, traces (OTel), rate limiting.</li> <li>Rotación de claves (KMS/HSMP si quieres subir nivel).</li> </ul> <p>Sistema de gestión de Cookies: Cambia en detalles operativos, no en el diseño. Con y sin subdominios seguirás usando OIDC (Authorization Code + PKCE), JWT, JWKS, etc. Lo que cambia es cómo gestionas cookies, CORS y “descubrir” si ya hay sesión.</p> <p>Comparativa rápida Tema    Con subdominios (cerebro.msark.net, bitcoin.msark.net)  Con dominios distintos (cerebro.com, bitcoin.app) Cookie de sesión SSO    Puedes compartir cookie si la emites con Domain=.msark.net. SameSite=Lax suele bastar (es same-site por eTLD+1). No puedes usar prefijo __Host- (exige no Domain).  No se pueden compartir cookies. La sesión vive solo en cerebro.com. El SSO se hace por redirecciones OIDC y tokens. Silent login / “ya estoy logado”    Muy fácil: al ser same-site, puedes hacer checks sin fricción (incluso iframes si quieres). OJO con la muerte de third-party cookies: el “silent SSO” vía iframe puede fallar. Usa OIDC con top-level redirects (prompt=none solo si el navegador concede acceso). Seguridad de cookies    Secure, HttpOnly, SameSite=Lax o Strict. Si usas iframes entre subdominios, quizá SameSite=None.    Igual en el IdP, pero las apps no ven la cookie del IdP. Todo va por code→token. CORS    Más simple: normalmente permitir *.msark.net.   Más explícito: whitelist de orígenes por dominio. Redirect URIs (OIDC)    Registrar todos los callbacks de cada subdominio en el cliente del IdP. Registrar todos los callbacks de cada dominio. Misma mecánica, solo más diversidad. Logout SSO  Puedes borrar la cookie de *.msark.net y notificar a cada app (front/back-channel). Logout en IdP y back-channel a cada app (revocar refresh). No hay cookie compartida que borrar en otras apps. Coste/infra DNS de subdominios es gratuito. SSL con Let’s Encrypt/Cloudflare también.   Igual: cada dominio/subdominio necesita DNS + SSL, pero no es caro si usas Let’s Encrypt/Cloudflare. Dev local   Puedes usar *.local.test y /etc/hosts para simular *.msark.net. Cada app con su host (cerebro.local.test, btc.local.test). No compartes cookie, pero el flujo OIDC es idéntico. Reglas prácticas</p> <p>Con subdominios</p> <p>Cookie SSO en el IdP: Set-Cookie: session=…; Domain=.msark.net; Path=/; Secure; HttpOnly; SameSite=Lax</p> <p>Recuerda: si pones Domain=, no uses __Host-.</p> <p>Pros: UX muy fluida y mínima fricción.</p> <p>Con dominios distintos (mi recomendación por universalidad)</p> <p>No dependas de cookies compartidas. Haz siempre: FE → redirección a cerebro → code → BE intercambia por access_token + refresh_token.</p> <p>Para detectar sesión previa, confía en la redirección (top-level). Evita iframes (por third-party cookie blocking).</p> <p>Cada BE valida JWT con JWKS y rota refresh en el IdP.</p>"}, {"location": "guias/", "title": "<PERSON><PERSON><PERSON>", "text": "<p>Esta sección contiene guías prácticas y tutoriales para el desarrollo y configuración del proyecto.</p>"}, {"location": "guias/#documentos-disponibles", "title": "📋 Documentos Disponibles", "text": ""}, {"location": "guias/#instalaciones", "title": "Instalaciones", "text": "<p>Guía rápida de instalación de herramientas y dependencias necesarias:</p> <ul> <li>Certificados web con mkcert</li> <li>Configuración de proyectos React con Vite</li> <li>Instalación de librerías de iconos</li> <li>Comandos útiles para desarrollo</li> </ul>"}, {"location": "guias/#herramientas-principales", "title": "🛠️ Herramientas Principales", "text": ""}, {"location": "guias/#certificados-ssl", "title": "Certificados SSL", "text": "<pre><code># Instalar mkcert\nsudo apt install -y mkcert libnss3-tools  # Ubuntu/Debian\nbrew install mkcert                        # macOS\n\n# Configurar CA local\nmkcert -install\n\n# Generar certificados para dominios\nmkcert -key-file traefik/dynamic/certs/MsArkNet-key.pem \\\n       -cert-file traefik/dynamic/certs/MsArkNet-cert.pem \\\n       MsArkNet.me grafana.MsArkNet.me prom.MsArkNet.me\n</code></pre>"}, {"location": "guias/#desarrollo-frontend", "title": "Desarrollo Frontend", "text": "<pre><code># Crear proyecto React con Vite\nPROJECT_NAME=health-be\nnpm create vite@latest \"$PROJECT_NAME\" -- --template react-swc-ts\n\n# Instalar iconos\nnpm install react-icons --save\n</code></pre>"}, {"location": "guias/#recursos-utiles", "title": "🔗 Recursos <PERSON>", "text": "<ul> <li>React Icons - Librería de iconos para React</li> <li>Vite - Build tool para desarrollo frontend</li> <li>mkcert - Herramienta para certificados SSL locales</li> </ul>"}, {"location": "guias/#proximas-guias", "title": "📚 Próximas Guías", "text": "<ul> <li>Configuración de entorno de desarrollo</li> <li>Guía de contribución al proyecto</li> <li>Mejores prácticas de desarrollo</li> <li>Configuración de IDE y herramientas</li> </ul>"}, {"location": "guias/instalaciones/", "title": "Certificados web", "text": "<p>sudo apt install -y mkcert libnss3-tools  # o brew install mkcert mkcert -install mkcert -key-file traefik/dynamic/certs/MsArkNet-key.pem \\        -cert-file traefik/dynamic/certs/MsArkNet-cert.pem \\        MsArkNet.me grafana.MsArkNet.me prom.MsArkNet.me</p>"}, {"location": "guias/instalaciones/#proyecto-react-vite", "title": "Proyecto react vite", "text": "<p>PROJECT_NAME=health-be npm create vite@latest “$PROJECT_NAME” – –template react-swc-ts</p>"}, {"location": "guias/instalaciones/#iconos", "title": "ICONOS", "text": "<p>https://react-icons.github.io/react-icons/ npm install react-icons –save</p>"}, {"location": "infraestructura/", "title": "MsArkNet - <PERSON><PERSON><PERSON> Docker con T<PERSON><PERSON><PERSON>", "text": "<p>Un entorno de desarrollo completo con Traefik como proxy reverso, SSL automático y múltiples servicios para desarrollo local.</p>"}, {"location": "infraestructura/#caracteristicas", "title": "🚀 Características", "text": "<ul> <li>Traefik v3.4 como proxy reverso con SSL automático</li> <li>Certificados SSL locales para todos los subdominios</li> <li>Dashboard de Traefik para monitorización</li> <li><PERSON><PERSON><PERSON><PERSON> servicios preconfigurados</li> <li>Escalabilidad horizontal de servicios</li> <li>Scripts automatizados para gestión completa</li> </ul>"}, {"location": "infraestructura/#servicios-disponibles", "title": "📋 Servicios Disponibles", "text": "Servicio URL Descripción Aplicación Principal https://MsArkNet.me Aplicación web principal (Nginx) API Backend https://MsArkNet.me/api Servicio API (Traefik/whoami) Dashboard Traefik https://traefik.MsArkNet.me Panel de control de Traefik Grafana Mock https://grafana.MsArkNet.me Simulación de Grafana Prometheus Mock https://prom.MsArkNet.me Simulación de Prometheus Portainer Mock https://portainer.MsArkNet.me Simulación de Portainer Documentación https://docs.MsArkNet.me Documentación del proyecto"}, {"location": "infraestructura/#prerrequisitos", "title": "🛠️ Prerrequisitos", "text": "<ul> <li>Docker &gt;= 20.x</li> <li>Docker Compose &gt;= 2.x</li> <li>OpenSSL (para generar certificados)</li> <li>Bash (para scripts de automatización)</li> </ul>"}, {"location": "infraestructura/#instalacion-rapida", "title": "⚡ Instalación Rápida", "text": "<pre><code># 1. <PERSON><PERSON><PERSON> o descargar el proyecto\ngit clone &lt;tu-repo&gt; MsArkNet\ncd MsArkNet\n\n# 2. Ejecutar setup automático\nchmod +x setup.sh\n./setup.sh\n\n# 3. ¡Listo! Los servicios estarán corriendo\n</code></pre> <p>El script de setup automáticamente:</p> <ul> <li>✅ Verifica dependencias</li> <li>✅ Crea estructura de directorios</li> <li>✅ Genera certificados SSL</li> <li>✅ Configura /etc/hosts</li> <li>✅ Crea red Docker</li> <li>✅ Inicia todos los servicios</li> </ul>"}, {"location": "infraestructura/#instalacion-manual", "title": "🔧 Instalación Manual", "text": "<p>Si prefieres configurar paso a paso:</p>"}, {"location": "infraestructura/#1-crear-estructura-de-directorios", "title": "1. Crear estructura de directorios", "text": "<pre><code>mkdir -p certs dynamic web-content/{main,portainer,docs} letsencrypt\n</code></pre>"}, {"location": "infraestructura/#2-generar-certificados-ssl", "title": "2. <PERSON><PERSON> certificados SSL", "text": "<pre><code>chmod +x generate-certs.sh\n./generate-certs.sh\n</code></pre>"}, {"location": "infraestructura/#3-configurar-etchosts", "title": "3. Configurar /etc/hosts", "text": "<p><PERSON><PERSON><PERSON> estas líneas a <code>/etc/hosts</code>:</p> <pre><code>127.0.0.1 MsArkNet.me\n127.0.0.1 grafana.MsArkNet.me\n127.0.0.1 prom.MsArkNet.me\n127.0.0.1 traefik.MsArkNet.me\n127.0.0.1 portainer.MsArkNet.me\n127.0.0.1 docs.MsArkNet.me\n</code></pre>"}, {"location": "infraestructura/#4-crear-red-docker-y-iniciar-servicios", "title": "4. <PERSON><PERSON><PERSON> y iniciar servicios", "text": "<pre><code>docker network create proxy\ndocker compose up -d\n</code></pre>"}, {"location": "infraestructura/#estructura-del-proyecto", "title": "📁 Estructura del Proyecto", "text": "<pre><code>MsArkNet/\n├── 📄 docker-compose.yml      # Configuración principal de servicios\n├── 📄 docker.mk              # Makefile con comandos Docker avanzados\n├── 📄 setup.sh               # Script de configuración automática\n├── 📄 generate-certs.sh      # Generador de certificados SSL\n├── 📁 certs/                 # Certificados SSL\n│   ├── MsArkNet.me.crt\n│   ├── MsArkNet.me.key\n│   └── MsArkNet.conf\n├── 📁 dynamic/               # Configuración dinámica de Traefik\n│   ├── tls.yml              # Configuración TLS\n│   └── middlewares.yml      # Middlewares personalizados\n├── 📁 web-content/          # Contenido web estático\n│   ├── main/               # Aplicación principal\n│   ├── portainer/          # Mo<PERSON> de Portainer\n│   └── docs/               # Documentación\n└── 📁 letsencrypt/         # Datos de Let's Encrypt (producción)\n</code></pre>"}, {"location": "infraestructura/#comandos-rapidos", "title": "🚀 Comand<PERSON>", "text": ""}, {"location": "infraestructura/#gestion-de-servicios", "title": "Gestión de servicios", "text": "<pre><code># Iniciar todos los servicios\ndocker compose up -d\n\n# Ver estado de servicios\ndocker compose ps\n\n# Ver logs en tiempo real\ndocker compose logs -f\n\n# Parar todos los servicios\ndocker compose down\n\n# Reiniciar un servicio específico\ndocker compose restart traefik\n</code></pre>"}, {"location": "infraestructura/#escalado-de-servicios", "title": "Escalado de servicios", "text": "<pre><code># Escalar API backend a 3 instancias\ndocker compose up -d --scale api-service=3\n\n# Escalar aplicación principal a 2 instancias\ndocker compose up -d --scale main-app=2\n</code></pre>"}, {"location": "infraestructura/#debugging-y-monitorizacion", "title": "Debugging y monitorización", "text": "<pre><code># Inspeccionar la red proxy\ndocker network inspect proxy\n\n# Ver configuración de Traefik\ncurl -s http://localhost:8080/api/rawdata | jq\n\n# Test de conectividad\ncurl -k -H \"Host: MsArkNet.me\" https://localhost/\n</code></pre>"}, {"location": "infraestructura/#enlaces-utiles", "title": "🔗 Enlaces Útiles", "text": "<ul> <li>Configuración detallada de Traefik</li> <li>Guía de certificados SSL</li> <li>Troubleshooting</li> </ul> <p>Documentación generada para el entorno de desarrollo local Stack: React + Node.js + Python + Docker + Traefik + Kubernetes</p>"}, {"location": "infraestructura/ssl-certificates/", "title": "Certificados SSL/TLS", "text": "<p>Guía completa para la gestión de certificados SSL/TLS en el entorno de desarrollo MsArkNet.</p>"}, {"location": "infraestructura/ssl-certificates/#vision-general", "title": "🔒 Visión General", "text": "<p>Los certificados SSL/TLS son esenciales para:</p> <ul> <li>Desarrollo local seguro con HTTPS</li> <li>Simulación del entorno de producción</li> <li>Compatibilidad con navegadores modernos</li> <li>Testing de funcionalidades que requieren HTTPS</li> </ul>"}, {"location": "infraestructura/ssl-certificates/#herramientas-utilizadas", "title": "🛠️ Herramientas Utilizadas", "text": ""}, {"location": "infraestructura/ssl-certificates/#mkcert", "title": "mkcert", "text": "<p>Herramienta para generar certificados SSL locales de confianza.</p> <pre><code># Instalación en Ubuntu/Debian\nsudo apt install -y mkcert libnss3-tools\n\n# Instalación en macOS\nbrew install mkcert\n\n# Instalación en Windows\nchoco install mkcert\n</code></pre>"}, {"location": "infraestructura/ssl-certificates/#configuracion-rapida", "title": "⚡ Configuración Rápida", "text": ""}, {"location": "infraestructura/ssl-certificates/#1-instalar-y-configurar-mkcert", "title": "1. <PERSON><PERSON><PERSON> y configurar mkcert", "text": "<pre><code># Instalar la CA local\nmkcert -install\n\n# Verificar instalación\nmkcert -CAROOT\n</code></pre>"}, {"location": "infraestructura/ssl-certificates/#2-generar-certificados-para-MsArkNet", "title": "2. <PERSON>rar certificados para MsArkNet", "text": "<pre><code># Generar certificados para todos los subdominios\nmkcert -key-file traefik/dynamic/certs/MsArkNet-key.pem \\\n       -cert-file traefik/dynamic/certs/MsArkNet-cert.pem \\\n       MsArkNet.me \\\n       \"*.MsArkNet.me\" \\\n       grafana.MsArkNet.me \\\n       prom.MsArkNet.me \\\n       traefik.MsArkNet.me \\\n       portainer.MsArkNet.me \\\n       docs.MsArkNet.me\n</code></pre>"}, {"location": "infraestructura/ssl-certificates/#3-configurar-traefik", "title": "3. <PERSON><PERSON><PERSON><PERSON>", "text": "<p>Los certificados se configuran automáticamente en Traefik a través del archivo <code>dynamic/tls.yml</code>:</p> <pre><code>tls:\n  certificates:\n    - certFile: /etc/traefik/certs/MsArkNet-cert.pem\n      keyFile: /etc/traefik/certs/MsArkNet-key.pem\n</code></pre>"}, {"location": "infraestructura/ssl-certificates/#estructura-de-certificados", "title": "📁 Estructura de Certificados", "text": "<pre><code>certs/\n├── MsArkNet-cert.pem     # Certificado público\n├── MsArkNet-key.pem      # Clave privada\n└── MsArkNet.conf         # Configuración (opcional)\n</code></pre>"}, {"location": "infraestructura/ssl-certificates/#configuracion-manual", "title": "🔧 Configuración Manual", "text": ""}, {"location": "infraestructura/ssl-certificates/#generar-certificados-con-openssl", "title": "Generar certificados con OpenSSL", "text": "<p>Si prefieres usar OpenSSL directamente:</p> <pre><code># Crear clave privada\nopenssl genrsa -out MsArkNet-key.pem 2048\n\n# Crear CSR (Certificate Signing Request)\nopenssl req -new -key MsArkNet-key.pem -out MsArkNet.csr \\\n  -subj \"/C=ES/ST=Madrid/L=Madrid/O=MsArkNet/CN=MsArkNet.me\"\n\n# Generar certificado autofirmado\nopenssl x509 -req -in MsArkNet.csr -signkey MsArkNet-key.pem \\\n  -out MsArkNet-cert.pem -days 365 \\\n  -extensions v3_req -extfile &lt;(cat &lt;&lt;EOF\n[v3_req]\nsubjectAltName = @alt_names\n[alt_names]\nDNS.1 = MsArkNet.me\nDNS.2 = *.MsArkNet.me\nDNS.3 = grafana.MsArkNet.me\nDNS.4 = prom.MsArkNet.me\nDNS.5 = traefik.MsArkNet.me\nEOF\n)\n</code></pre>"}, {"location": "infraestructura/ssl-certificates/#configuracion-del-navegador", "title": "🌐 Configuración del Navegador", "text": ""}, {"location": "infraestructura/ssl-certificates/#chromechromium", "title": "Chrome/Chromium", "text": "<ol> <li>Ir a <code>chrome://settings/certificates</code></li> <li>Pestaña “Authorities”</li> <li>Importar el certificado CA de mkcert</li> <li>Marcar “Trust this certificate for identifying websites”</li> </ol>"}, {"location": "infraestructura/ssl-certificates/#firefox", "title": "Firefox", "text": "<ol> <li>Ir a <code>about:preferences#privacy</code></li> <li>Sección “Certificates” → “View Certificates”</li> <li>Pestaña “Authorities”</li> <li>Importar el certificado CA de mkcert</li> </ol>"}, {"location": "infraestructura/ssl-certificates/#safari-macos", "title": "Safari (macOS)", "text": "<ol> <li>Abrir “Keychain Access”</li> <li>Buscar “mkcert”</li> <li>Doble clic en el certificado</li> <li>Expandir “Trust”</li> <li>Cambiar “When using this certificate” a “Always Trust”</li> </ol>"}, {"location": "infraestructura/ssl-certificates/#renovacion-de-certificados", "title": "🔄 Renovación de Certificados", "text": ""}, {"location": "infraestructura/ssl-certificates/#automatica-con-mkcert", "title": "Automática con mkcert", "text": "<p>Los certificados generados con mkcert tienen una validez de 2 años y 3 meses. Para renovar:</p> <pre><code># Regenerar certificados\n./generate-certs.sh\n\n# Reiniciar Traefik para cargar nuevos certificados\ndocker compose restart traefik\n</code></pre>"}, {"location": "infraestructura/ssl-certificates/#script-de-renovacion-automatica", "title": "Script de renovación automática", "text": "<pre><code>#!/bin/bash\n# renew-certs.sh\n\necho \"🔄 Renovando certificados SSL...\"\n\n# Backup de certificados actuales\ncp certs/MsArkNet-cert.pem certs/MsArkNet-cert.pem.backup\ncp certs/MsArkNet-key.pem certs/MsArkNet-key.pem.backup\n\n# Generar nuevos certificados\nmkcert -key-file certs/MsArkNet-key.pem \\\n       -cert-file certs/MsArkNet-cert.pem \\\n       MsArkNet.me \"*.MsArkNet.me\"\n\n# Reiniciar Traefik\ndocker compose restart traefik\n\necho \"✅ Certificados renovados correctamente\"\n</code></pre>"}, {"location": "infraestructura/ssl-certificates/#verificacion-y-testing", "title": "🔍 Verificación y Testing", "text": ""}, {"location": "infraestructura/ssl-certificates/#verificar-certificados", "title": "Verificar certificados", "text": "<pre><code># Verificar información del certificado\nopenssl x509 -in certs/MsArkNet-cert.pem -text -noout\n\n# Verificar fechas de validez\nopenssl x509 -in certs/MsArkNet-cert.pem -dates -noout\n\n# Test de conectividad HTTPS\ncurl -k -I https://MsArkNet.me\n</code></pre>"}, {"location": "infraestructura/ssl-certificates/#debugging-de-problemas-ssl", "title": "Debugging de problemas SSL", "text": "<pre><code># Test detallado de SSL\nopenssl s_client -connect MsArkNet.me:443 -servername MsArkNet.me\n\n# Verificar configuración de Traefik\ncurl -s http://localhost:8080/api/rawdata | jq '.tls'\n</code></pre>"}, {"location": "infraestructura/ssl-certificates/#consideraciones-de-seguridad", "title": "⚠️ Consideraciones de Seguridad", "text": ""}, {"location": "infraestructura/ssl-certificates/#desarrollo-vs-produccion", "title": "Desarrollo vs Producción", "text": "Aspecto Desarrollo Producción Certificados Autofirmados (mkcert) Let’s Encrypt / CA comercial Validez 2+ años 90 días (Let’s Encrypt) Renovación Manual Automática Confianza Solo local Globalmente confiable"}, {"location": "infraestructura/ssl-certificates/#buenas-practicas", "title": "Buenas Prácticas", "text": "<ul> <li>✅ Nunca commitear claves privadas al repositorio</li> <li>✅ Usar <code>.gitignore</code> para excluir archivos de certificados</li> <li>✅ Rotar certificados regularmente</li> <li>✅ Usar certificados diferentes para cada entorno</li> <li>✅ Monitorizar fechas de expiración</li> </ul>"}, {"location": "infraestructura/ssl-certificates/#enlaces-utiles", "title": "🔗 Enlaces Útiles", "text": "<ul> <li>mkcert GitHub</li> <li>Traefik TLS Documentation</li> <li>Let’s Encrypt</li> <li>OpenSSL Documentation</li> </ul>"}, {"location": "infraestructura/traefik-setup/", "title": "🚀 Configuración de Traefik", "text": "<p>Traefik está configurado para enrutar automáticamente el tráfico HTTPS usando certificados locales.</p> <p>💡 Tip</p> <p>Todos los servicios están disponibles a través de HTTPS en el dominio <code>*.MsArkNet.me</code></p>"}, {"location": "infraestructura/traefik-setup/#servicios-disponibles", "title": "<PERSON><PERSON><PERSON>", "text": "Servicio URL Descripción Estado Aplicación Principal <code>https://MsArkNet.me</code> Frontend principal ✅ Activo Dashboard Traefik <code>https://traefik.MsArkNet.me</code> Panel de control ✅ Activo Grafana <code>https://grafana.MsArkNet.me</code> Métricas y dashboards ✅ Activo Prometheus <code>https://prom.MsArkNet.me</code> Monitorización ✅ Activo"}, {"location": "infraestructura/traefik-setup/#api-endpoints", "title": "🔧 API Endpoints", "text": "<p>Documentación de los endpoints disponibles para desarrollo.</p>"}, {"location": "infraestructura/traefik-setup/#endpoints-principales", "title": "Endpoints Principales", "text": ""}, {"location": "infraestructura/traefik-setup/#get-api", "title": "GET <code>/api</code>", "text": "<p>Endpoint principal de la API - devuelve información del servicio</p>"}, {"location": "infraestructura/traefik-setup/#get-apihealth", "title": "GET <code>/api/health</code>", "text": "<p>Health check del servicio</p>"}, {"location": "infraestructura/traefik-setup/#post-apidata", "title": "POST <code>/api/data</code>", "text": "<p>Endpoint para recibir datos (simulado)</p>"}, {"location": "infraestructura/traefik-setup/#ejemplo-de-respuesta", "title": "Ejemplo de respuesta", "text": "<pre><code>{\n  \"service\": \"API Backend Service\",\n  \"version\": \"1.0.0\",\n  \"timestamp\": \"2025-09-12T10:30:00Z\",\n  \"environment\": \"development\",\n  \"ssl\": true,\n  \"cors_enabled\": true\n}\n</code></pre>"}, {"location": "infraestructura/traefik-setup/#configuracion-docker", "title": "🐳 Configuración Docker", "text": "<p>Comandos útiles para gestionar el entorno de desarrollo.</p>"}, {"location": "infraestructura/traefik-setup/#comandos-principales", "title": "Comandos principales", "text": "<pre><code># Iniciar todos los servicios\ndocker compose up -d\n\n# Ver logs de Traefik\ndocker compose logs -f traefik\n\n# Reiniciar un servicio específico\ndocker compose restart main-app\n\n# Escalar un servicio\ndocker compose up -d --scale api-service=3\n\n# Parar todos los servicios\ndocker compose down\n</code></pre>"}, {"location": "infraestructura/traefik-setup/#red-docker", "title": "<PERSON>", "text": "Configuración Valor Nombre de red <code>proxy</code> Driver bridge Subnet **********/16"}, {"location": "infraestructura/traefik-setup/#certificados-ssltls", "title": "🔒 Certificados SSL/TLS", "text": "<p>Configuración de certificados para desarrollo local seguro.</p> <p>⚠️ Importante</p> <p>Los certificados son autofirmados para desarrollo local. En producción usar Let’s Encrypt.</p>"}, {"location": "infraestructura/traefik-setup/#generar-nuevos-certificados", "title": "Generar nuevos certificados", "text": "<pre><code># <PERSON><PERSON> ejecutable el script\nchmod +x generate-certs.sh\n\n# Ejecutar generación de certificados\n./generate-certs.sh\n</code></pre>"}, {"location": "infraestructura/traefik-setup/#configuracion-de-navegador", "title": "Configuración de navegador", "text": "<ol> <li>Accede a cualquier servicio HTTPS</li> <li>Haz clic en “Avanzado” cuando aparezca la advertencia</li> <li>Selecciona “Continuar a MsArkNet.me (no es seguro)”</li> <li>El certificado se recordará para futuras visitas</li> </ol>"}, {"location": "infraestructura/traefik-setup/#troubleshooting", "title": "🔍 Troubleshooting", "text": ""}, {"location": "infraestructura/traefik-setup/#problemas-comunes", "title": "Problemas comunes", "text": ""}, {"location": "infraestructura/traefik-setup/#error-this-site-cant-be-reached", "title": "❌ Error: “This site can’t be reached”", "text": "<ul> <li>Verificar que los servicios están ejecutándose: <code>docker compose ps</code></li> <li>Comprobar el archivo <code>/etc/hosts</code></li> <li>Reiniciar Traefik: <code>docker compose restart traefik</code></li> </ul>"}, {"location": "infraestructura/traefik-setup/#error-de-certificado-ssl", "title": "❌ Error de certificado SSL", "text": "<ul> <li>Regenerar certificados: <code>./generate-certs.sh</code></li> <li>Limpiar caché del navegador</li> <li>Verificar que los archivos están en <code>./certs/</code></li> </ul>"}, {"location": "infraestructura/traefik-setup/#servicios-no-responden", "title": "❌ Ser<PERSON>ios no responden", "text": "<ul> <li>Verificar logs: <code>docker compose logs [servicio]</code></li> <li>Comprobar red Docker: <code>docker network ls</code></li> <li>Reiniciar completamente: <code>docker compose down &amp;&amp; docker compose up -d</code></li> </ul>"}, {"location": "infraestructura/traefik-setup/#comandos-utiles-para-debugging", "title": "Comandos útiles para debugging", "text": "<pre><code># Ver estado de todos los contenedores\ndocker compose ps\n\n# Inspeccionar la red proxy\ndocker network inspect proxy\n\n# Ver configuración de Traefik\ncurl -s http://localhost:8080/api/rawdata | jq\n\n# Test de conectividad\ncurl -k -H \"Host: MsArkNet.me\" https://localhost/\n</code></pre>"}, {"location": "infraestructura/traefik-setup/#orden-recomendado-en-docker-composeyml", "title": "📝 Orden recomendado en docker-compose.yml", "text": "<pre><code># Orden recomendado dentro de un servicio:\ncontainer_name\nimage / build\ncommand / entrypoint\nrestart\nenvironment / env_file\nports\nvolumes\nnetworks\ndepends_on\nhealthcheck\nlabels\n# Otros (ulimits, secrets, etc.)\n</code></pre> <p>Stack: React + Node.js + Python + Docker + Traefik + Kubernetes</p>"}, {"location": "infraestructura/troubleshooting/", "title": "Troubleshooting", "text": "<p>Guía completa para resolver problemas comunes en el entorno de desarrollo MsArkNet.</p>"}, {"location": "infraestructura/troubleshooting/#problemas-comunes", "title": "🔍 <PERSON>as Comunes", "text": ""}, {"location": "infraestructura/troubleshooting/#error-this-site-cant-be-reached", "title": "❌ Error: “This site can’t be reached”", "text": "<p>Síntomas: - El navegador no puede conectar con <code>https://MsArkNet.me</code> - Timeout de conexión - DNS_PROBE_FINISHED_NXDOMAIN</p> <p>Soluciones:</p> <ol> <li> <p>Verificar servicios Docker <pre><code># Ver estado de contenedores\ndocker compose ps\n\n# Si algún servicio está down\ndocker compose up -d\n</code></pre></p> </li> <li> <p>Comprobar archivo /etc/hosts <pre><code># Verificar entradas\ncat /etc/hosts | grep MsArkNet\n\n# Debería mostrar:\n# 127.0.0.1 MsArkNet.me\n# 127.0.0.1 grafana.MsArkNet.me\n# 127.0.0.1 prom.MsArkNet.me\n# 127.0.0.1 traefik.MsArkNet.me\n</code></pre></p> </li> <li> <p>Reiniciar Traefik <pre><code>docker compose restart traefik\ndocker compose logs -f traefik\n</code></pre></p> </li> </ol>"}, {"location": "infraestructura/troubleshooting/#error-de-certificado-ssl", "title": "❌ Error de certificado SSL", "text": "<p>Síntomas: - “Your connection is not private” - NET::ERR_CERT_AUTHORITY_INVALID - SSL_ERROR_BAD_CERT_DOMAIN</p> <p>Soluciones:</p> <ol> <li> <p>Regenerar certificados <pre><code># Ejecutar script de generación\nchmod +x generate-certs.sh\n./generate-certs.sh\n\n# Reiniciar Traefik\ndocker compose restart traefik\n</code></pre></p> </li> <li> <p>Verificar certificados <pre><code># Comprobar que existen los archivos\nls -la certs/\n\n# Verificar contenido del certificado\nopenssl x509 -in certs/MsArkNet-cert.pem -text -noout\n</code></pre></p> </li> <li> <p>Limpiar caché del navegador</p> </li> <li>Chrome: <code>Ctrl+Shift+Delete</code> → Borrar datos de navegación</li> <li>Firefox: <code>Ctrl+Shift+Delete</code> → Limpiar historial reciente</li> <li> <p>Safari: Desarrollar → Vaciar cachés</p> </li> <li> <p>Reinstalar CA de mkcert <pre><code>mkcert -uninstall\nmkcert -install\n</code></pre></p> </li> </ol>"}, {"location": "infraestructura/troubleshooting/#servicios-no-responden", "title": "❌ Ser<PERSON>ios no responden", "text": "<p>Síntomas: - 502 Bad Gateway - 503 Service Unavailable - Timeout en respuestas</p> <p>Soluciones:</p> <ol> <li> <p>Verificar logs de servicios <pre><code># Ver logs de todos los servicios\ndocker compose logs\n\n# Ver logs de un servicio específico\ndocker compose logs -f main-app\ndocker compose logs -f api-service\n</code></pre></p> </li> <li> <p>Comprobar red Docker <pre><code># Verificar red proxy\ndocker network ls\ndocker network inspect proxy\n\n# Recrear red si es necesario\ndocker network rm proxy\ndocker network create proxy\n</code></pre></p> </li> <li> <p>Reiniciar completamente <pre><code># Parar todos los servicios\ndocker compose down\n\n# Limpiar contenedores y volúmenes\ndocker compose down -v --remove-orphans\n\n# Iniciar de nuevo\ndocker compose up -d\n</code></pre></p> </li> </ol>"}, {"location": "infraestructura/troubleshooting/#puerto-ya-en-uso", "title": "❌ Puerto ya en uso", "text": "<p>Síntomas: - “Port is already allocated” - “Address already in use”</p> <p>Soluciones:</p> <ol> <li> <p>Identificar proceso que usa el puerto <pre><code># Ver qué proceso usa el puerto 80/443\nsudo netstat -tulpn | grep :80\nsudo netstat -tulpn | grep :443\n\n# O usar lsof\nsudo lsof -i :80\nsudo lsof -i :443\n</code></pre></p> </li> <li> <p>Terminar proceso conflictivo <pre><code># Matar proceso por PID\nsudo kill -9 &lt;PID&gt;\n\n# O parar servicio específico\nsudo systemctl stop apache2\nsudo systemctl stop nginx\n</code></pre></p> </li> </ol>"}, {"location": "infraestructura/troubleshooting/#problemas-de-permisos", "title": "❌ Problemas de permisos", "text": "<p>Síntomas: - “Permission denied” - “Cannot create directory” - Errores al escribir archivos</p> <p>Soluciones:</p> <ol> <li> <p>Verificar permisos de directorios <pre><code># Comprobar permisos\nls -la certs/\nls -la dynamic/\n\n# Corregir permisos si es necesario\nchmod 755 certs/\nchmod 644 certs/*.pem\n</code></pre></p> </li> <li> <p>Problemas con Docker <pre><code># Añadir usuario al grupo docker\nsudo usermod -aG docker $USER\n\n# Reiniciar sesión o ejecutar\nnewgrp docker\n</code></pre></p> </li> </ol>"}, {"location": "infraestructura/troubleshooting/#comandos-de-debugging", "title": "🛠️ Comandos de Debugging", "text": ""}, {"location": "infraestructura/troubleshooting/#informacion-del-sistema", "title": "Información del sistema", "text": "<pre><code># Información de Docker\ndocker version\ndocker compose version\n\n# Estado de servicios\ndocker compose ps\ndocker stats\n\n# Información de red\ndocker network ls\nip addr show\n</code></pre>"}, {"location": "infraestructura/troubleshooting/#logs-y-monitorizacion", "title": "Logs y monitorización", "text": "<pre><code># Logs en tiempo real\ndocker compose logs -f\n\n# Logs de un servicio específico\ndocker compose logs -f traefik\n\n# Logs con timestamp\ndocker compose logs -t\n\n# Últimas 100 líneas de logs\ndocker compose logs --tail=100\n</code></pre>"}, {"location": "infraestructura/troubleshooting/#testing-de-conectividad", "title": "Testing de conectividad", "text": "<pre><code># Test básico de conectividad\ncurl -I http://localhost\ncurl -k -I https://localhost\n\n# Test con headers específicos\ncurl -k -H \"Host: MsArkNet.me\" https://localhost/\n\n# Test de API\ncurl -k https://MsArkNet.me/api\n\n# Test de DNS local\nnslookup MsArkNet.me\ndig MsArkNet.me\n</code></pre>"}, {"location": "infraestructura/troubleshooting/#inspeccion-de-traefik", "title": "Inspección de Traefik", "text": "<pre><code># API de Traefik (si está habilitada)\ncurl -s http://localhost:8080/api/rawdata | jq\n\n# Ver configuración de routers\ncurl -s http://localhost:8080/api/http/routers | jq\n\n# Ver servicios registrados\ncurl -s http://localhost:8080/api/http/services | jq\n\n# Ver middlewares\ncurl -s http://localhost:8080/api/http/middlewares | jq\n</code></pre>"}, {"location": "infraestructura/troubleshooting/#scripts-de-diagnostico", "title": "🔧 Scripts de Diagnóstico", "text": ""}, {"location": "infraestructura/troubleshooting/#script-de-verificacion-completa", "title": "Script de verificación completa", "text": "<pre><code>#!/bin/bash\n# diagnose.sh\n\necho \"🔍 Diagnóstico del entorno MsArkNet\"\necho \"==================================\"\n\necho \"📋 1. Verificando Docker...\"\ndocker --version\ndocker compose version\n\necho \"📋 2. Estado de contenedores...\"\ndocker compose ps\n\necho \"📋 3. Verificando red proxy...\"\ndocker network inspect proxy &gt; /dev/null 2&gt;&amp;1\nif [ $? -eq 0 ]; then\n    echo \"✅ Red proxy existe\"\nelse\n    echo \"❌ Red proxy no existe\"\nfi\n\necho \"📋 4. Verificando /etc/hosts...\"\ngrep -q \"MsArkNet.me\" /etc/hosts\nif [ $? -eq 0 ]; then\n    echo \"✅ Entradas en /etc/hosts encontradas\"\nelse\n    echo \"❌ Faltan entradas en /etc/hosts\"\nfi\n\necho \"📋 5. Verificando certificados...\"\nif [ -f \"certs/MsArkNet-cert.pem\" ]; then\n    echo \"✅ Certificado encontrado\"\n    openssl x509 -in certs/MsArkNet-cert.pem -dates -noout\nelse\n    echo \"❌ Certificado no encontrado\"\nfi\n\necho \"📋 6. Test de conectividad...\"\ncurl -k -s -o /dev/null -w \"%{http_code}\" https://localhost/ | grep -q \"200\\|301\\|302\"\nif [ $? -eq 0 ]; then\n    echo \"✅ Conectividad HTTPS OK\"\nelse\n    echo \"❌ Problemas de conectividad HTTPS\"\nfi\n\necho \"==================================\"\necho \"🏁 Diagnóstico completado\"\n</code></pre>"}, {"location": "infraestructura/troubleshooting/#script-de-limpieza", "title": "Script de limpieza", "text": "<pre><code>#!/bin/bash\n# cleanup.sh\n\necho \"🧹 Limpiando entorno MsArkNet...\"\n\n# Parar servicios\ndocker compose down\n\n# Limpiar contenedores huérfanos\ndocker compose down --remove-orphans\n\n# Limpiar volúmenes no utilizados\ndocker volume prune -f\n\n# Limpiar imágenes no utilizadas\ndocker image prune -f\n\n# Limpiar red si existe\ndocker network rm proxy 2&gt;/dev/null || true\n\necho \"✅ Limpieza completada\"\n</code></pre>"}, {"location": "infraestructura/troubleshooting/#obtener-ayuda", "title": "📞 Obtener Ayuda", "text": ""}, {"location": "infraestructura/troubleshooting/#informacion-para-reportar-problemas", "title": "Información para reportar problemas", "text": "<p><PERSON><PERSON><PERSON> reportes un problema, incluye:</p> <ol> <li> <p>Versión del sistema <pre><code>uname -a\ndocker --version\ndocker compose version\n</code></pre></p> </li> <li> <p>Estado de servicios <pre><code>docker compose ps\ndocker compose logs --tail=50\n</code></pre></p> </li> <li> <p>Configuración de red <pre><code>docker network ls\nip addr show\ncat /etc/hosts | grep MsArkNet\n</code></pre></p> </li> <li> <p>Logs específicos del error <pre><code>docker compose logs traefik\n</code></pre></p> </li> </ol>"}, {"location": "infraestructura/troubleshooting/#recursos-adicionales", "title": "Recursos adicionales", "text": "<ul> <li>Documentación de Docker</li> <li>Documentación de Traefik</li> <li>Issues del proyecto</li> <li>Discord de la comunidad</li> </ul>"}, {"location": "sprints/", "title": "Plan de Sprints", "text": "<p>Esta sección contiene la planificación y seguimiento de los sprints del proyecto SSO “Cerebro”.</p>"}, {"location": "sprints/#objetivo-general", "title": "🎯 Objetivo General", "text": "<p>Entregar un IdP OIDC (Cerebro) + panel frontend (cerebro‑fe) + SDK/middleware para -be clientes con:</p> <ul> <li>Auth Code + PKCE</li> <li>SSO + back‑channel logout</li> <li>RBAC/consent</li> <li>Observabilidad</li> <li>Rotación de claves</li> <li>Swagger para pruebas</li> </ul>"}, {"location": "sprints/#documentos-disponibles", "title": "📋 Documentos Disponibles", "text": ""}, {"location": "sprints/#plan-detallado-de-sprints", "title": "Plan Detallado de Sprints", "text": "<p>Planificación completa dividida en sprints con objetivos específicos:</p> <ul> <li>Sprint 0: Fundaciones &amp; Infraestructura</li> <li>Sprint 1: Core OIDC + Auth Code/PKCE</li> <li>Sprint 2: JWT + JWKS + Middleware</li> <li>Sprint 3: SSO + Back-channel Logout</li> <li>Sprint 4: RBAC + Consent + Panel FE</li> <li>Sprint 5: Observabilidad + Rotación de Claves</li> </ul>"}, {"location": "sprints/#estructura-de-repositorios", "title": "🏗️ Estructura de Repositorios", "text": "<pre><code>apps/\n├── cerebro-idp/          # NestJS/Express + Prisma - Proveedor OIDC\n├── cerebro-fe/           # React + Vite + TS - Panel de gestión\n├── abogados-be/          # Resource server de ejemplo\n└── bitcoin-be/           # Resource server de ejemplo\n\npackages/\n└── auth-mw/              # Middleware/SDK validación JWT\n\ninfra/\n└── docker-compose/       # MariaDB, Redis, Traefik, OTEL, etc.\n</code></pre>"}, {"location": "sprints/#estado-actual", "title": "📊 Estado Actual", "text": "<ul> <li>✅ Infraestructura base con Docker y Traefik</li> <li>🔄 Configuración de certificados SSL</li> <li>⏳ Implementación del IdP OIDC</li> <li>⏳ Desarrollo del panel frontend</li> </ul>"}, {"location": "sprints/#enlaces-relacionados", "title": "🔗 Enlaces Relacionados", "text": "<ul> <li>Checklist de Implementación</li> <li>Configuración de Infraestructura</li> <li>Guías de Instalación</li> </ul>"}, {"location": "sprints/plan-sprints/", "title": "Plan de Sprints — SSO “Cerebro” (FE + BE en paralelo)", "text": "<p>Objetivo: entregar un IdP OIDC (Cerebro) + panel frontend (cerebro‑fe) + SDK/middleware para -be clientes (abogados‑be, bitcoin‑be) con Auth Code + PKCE, SSO + back‑channel logout, RBAC/consent, observabilidad, rotación de claves y Swagger para pruebas.</p>"}, {"location": "sprints/plan-sprints/#estructura-de-repos-sugerida", "title": "Estructura de repos (sugerida)", "text": "<ul> <li>apps/cerebro-idp (NestJS/Express + Prisma) — Proveedor OIDC.</li> <li>apps/cerebro-fe (React + Vite + TS) — Panel de sesiones, dispositivos, consent, admin RBAC.</li> <li>apps/abogados-be y apps/bitcoin-be — Resource servers de ejemplo (Swagger + middleware JWT).</li> <li>packages/auth-mw — Middleware/SDK validación JWT + cache JWKS (Node/Express/Nest).</li> <li>infra/ — Docker Compose (MariaDB, Redis, Traefik + Let’s Encrypt, OTEL Collector, Prom/Grafana/Loki), seeders, migraciones.</li> </ul>"}, {"location": "sprints/plan-sprints/#sprint-0-fundaciones-infra", "title": "Sprint 0 — Fundaciones &amp; Infra", "text": "<p>Objetivo: Entorno reproducible y baseline de calidad.</p> <p>Entregables</p> <ul> <li>Docker Compose con MariaDB, <PERSON>is, <PERSON><PERSON><PERSON>k (TLS Let’s Encrypt), OTel Collector, Prometheus, Grafana, Loki.</li> <li>Repos iniciales + CI (lint/test/build) + quality gates.</li> <li>Prisma conectado a MariaDB, migración inicial + seed oauth_clients (bitcoin, abogados, cerebro‑fe).</li> <li>Estructura de .env.example (secretos por servicio) + guía de secretos.</li> <li>Swagger básico vivo en cada -be (hello + /health).</li> </ul> <p>Tareas</p> <ul> <li>Scaffold repos + monorepo (pnpm/lerna/turbo) o multi‑repo (a elegir).</li> <li>Traefik reglas + certificados LE wildcard.</li> <li>Prisma schema (users, sessions, oauth_clients, oauth_codes, tokens, jwk_keys, consents, roles, permissions, audit_logs).</li> <li>Seed inicial de clientes con redirect_uris y backchannel_logout_uri.</li> <li>Telemetry bootstrap (OTel SDK), structured logging (pino) y correlation-id.</li> </ul> <p>DoD</p> <ul> <li><code>docker compose up</code> levanta todo con TLS.</li> <li><code>prisma migrate deploy</code> + <code>prisma db seed</code> OK.</li> <li><code>/health</code> y <code>/metrics</code> accesibles por cada servicio.</li> </ul>"}, {"location": "sprints/plan-sprints/#sprint-1-oidc-discovery-jwks-authorization-endpoint-pkce", "title": "Sprint 1 — OIDC Discovery + JWKS + Authorization Endpoint (PKCE)", "text": "<p>Objetivo: Implementar núcleo OIDC publicable.</p> <p>Entregables</p> <ul> <li><code>/.well-known/openid-configuration</code> completo.</li> <li><code>/jwks.json</code> con kid y storage en DB (tabla <code>jwk_keys</code>).</li> <li><code>/authorize</code> con Authorization Code + PKCE (S256), consent mínimo stub.</li> <li>UI cerebro‑fe: Login, Selector de cliente, pantalla de Consent (stub).</li> </ul> <p>Tareas</p> <ul> <li>Generación/rotación manual de par de claves (RSA/ECDSA) y persistencia.</li> <li>Validaciones PKCE (code_verifier, code_challenge).</li> <li>Persistencia de <code>authorization_code</code> (tabla <code>oauth_codes</code>).</li> <li>UI: formularios, estado, redirecciones con <code>state</code> y <code>nonce</code>.</li> <li>Tests integración: flujos <code>/authorize</code> happy path + errores (missing redirect_uri, invalid_client, invalid_scope, pkce_invalid).</li> </ul> <p>DoD</p> <ul> <li>OpenID Provider Metadata válida en OIDC playground.</li> <li>Se emite <code>code</code> y redirige correctamente al <code>redirect_uri</code> del cliente.</li> </ul>"}, {"location": "sprints/plan-sprints/#sprint-2-token-endpoint-emision-jwt-idaccess-middleware-jwt", "title": "Sprint 2 — Token Endpoint + Emisión JWT (ID/Access) + Middleware JWT", "text": "<p>Objetivo: Completar Auth Code Flow end‑to‑end y consumo por APIs.</p> <p>Entregables</p> <ul> <li><code>/token</code> con <code>authorization_code</code> + PKCE y refresh_token (rotación activada).</li> <li>Emisión ID Token y Access Token JWT con <code>kid</code>, <code>aud</code> por cliente, <code>scope</code>/permissions y <code>exp</code>.</li> <li>packages/auth-mw: middleware de validación JWT (firma, <code>aud</code>, <code>exp</code>, <code>nbf</code>) + cache JWKS con TTL + fallback.</li> <li>Swagger en abogados‑be/bitcoin‑be con OAuth2 Auth Code para probar.</li> </ul> <p>Tareas</p> <ul> <li>Claims: <code>iss</code>, <code>sub</code>, <code>aud</code>, <code>azp</code>, <code>iat</code>, <code>exp</code>, <code>auth_time</code>, <code>amr</code>, <code>nonce</code> (si aplica), <code>scope</code>, <code>roles</code> (si ya disponibles), <code>sid</code>.</li> <li>Refresh token rotation + revocación en DB (tabla <code>tokens</code>).</li> <li>UI: flujo completo login → consent → redirect al cliente demo.</li> <li>Tests: firma JWS, invalid aud, token expirado, JWKS cache miss/hit.</li> </ul> <p>DoD</p> <ul> <li>Cliente demo llama a abogados‑be/bitcoin‑be con <code>Authorization: Bearer</code> y pasa middleware.</li> </ul>"}, {"location": "sprints/plan-sprints/#sprint-3-sso-logout-gestion-de-sesiones-fe", "title": "Sprint 3 — SSO Logout + Gestión de Sesiones (FE)", "text": "<p>Objetivo: Cerrar sesiones globalmente y visibilidad al usuario.</p> <p>Entregables</p> <ul> <li>Front‑channel y back‑channel logout: notificación a <code>backchannel_logout_uri</code> y limpieza de <code>sid</code>.</li> <li>Panel cerebro‑fe: sesiones activas, dispositivos, cerrar sesión por dispositivo, revocar refresh tokens.</li> <li>Endpoint <code>/revocation</code> (RFC 7009) y <code>/introspect</code> (opcional para debug).</li> </ul> <p>Tareas</p> <ul> <li>Modelo de session store en Redis (sid ↔ user ↔ clients ↔ tokens).</li> <li>Auditoría de eventos: login, token_issued, refresh_rotated, logout, revoke.</li> <li>UI: tabla de sesiones, device fingerprint básico, acciones.</li> <li>Tests e2e (Playwright): login multi‑cliente, logout global, back‑channel recibido por clientes demo.</li> </ul> <p>DoD</p> <ul> <li>Al hacer logout global, clientes reciben back‑channel y niegan tokens siguientes.</li> </ul>"}, {"location": "sprints/plan-sprints/#sprint-4-rbacpermissions-consent-real-por-cliente", "title": "Sprint 4 — RBAC/Permissions + Consent real por cliente", "text": "<p>Objetivo: Control fino de acceso y consentimiento configurable.</p> <p>Entregables</p> <ul> <li>Modelo RBAC: <code>roles</code>, <code>permissions</code>, <code>role_permissions</code>, <code>user_roles</code> por cliente.</li> <li>Consent UI: scopes/grants por cliente; recordar decisión.</li> <li>Mapeo de roles/permissions → claims en Access/ID token.</li> <li>Panel admin en cerebro‑fe: asignar roles a usuarios, ver permisos por cliente.</li> </ul> <p>Tareas</p> <ul> <li>Migraciones Prisma + seed roles/permisos base.</li> <li>Política de scopes (<code>openid email profile offline_access …</code> + custom <code>abogados:read</code>, <code>bitcoin:tx:*</code>).</li> <li>Filtros por <code>aud</code> y <code>scope</code> en middleware.</li> <li>Tests: cambio de rol refleja claims; denegación por falta de permiso.</li> </ul> <p>DoD</p> <ul> <li>Un usuario sin permiso no puede acceder a endpoint protegido aunque tenga token válido.</li> </ul>"}, {"location": "sprints/plan-sprints/#sprint-5-observabilidad-seguridad-operacional", "title": "Sprint 5 — Observabilidad &amp; Seguridad Operacional", "text": "<p>Objetivo: Visibilidad total + límites de abuso.</p> <p>Entregables</p> <ul> <li>Audit logs consultables en Grafana/Loki.</li> <li>Métricas (Prometheus): tasas de login, errores por tipo, latencias, 95/99p; cardinalidad controlada.</li> <li>Traces OTel end‑to‑end (idp ↔ clientes) con baggage <code>user_id</code> (anonimizado) y <code>request_id</code>.</li> <li>Rate limiting por IP/cliente/usuario para <code>/authorize</code>, <code>/token</code>, <code>/jwks</code>, <code>/introspect</code>, <code>/revocation</code>.</li> </ul> <p>Tareas</p> <ul> <li>Exporters OTel → Collector → Prom/Grafana.</li> <li>Dashboards: salud OIDC, picos de error, top clientes.</li> <li>Límites por Redis (token bucket) + respuestas RFC (429 con <code>Retry-After</code>).</li> <li>Hardening: headers de seguridad, CSP en FE, cookie flags, SameSite.</li> </ul> <p>DoD</p> <ul> <li>Tableros listos + alertas básicas (p99 &gt; umbral, 5xx burst, fallo LE renew).</li> </ul>"}, {"location": "sprints/plan-sprints/#sprint-6-rotacion-de-claves-kmshsm-nivel-pro-docs", "title": "Sprint 6 — Rotación de Claves + KMS/HSM (nivel pro) + Docs", "text": "<p>Objetivo: Seguridad criptográfica y operativa a largo plazo.</p> <p>Entregables</p> <ul> <li>Política de rotación de claves (programada) y retiro seguro de claves antiguas.</li> <li>Integración opcional con KMS/HSM (abstracción de firma JWS).</li> <li>Runbooks y Swagger completo (con ejemplos por flujo) + guía de integración para clientes.</li> </ul> <p><PERSON>rea<PERSON></p> <ul> <li>Tabla <code>jwk_keys</code>: estados (active, retired), <code>kid</code> inmutable, ventana de solape JWKS.</li> <li>Job de rotación (cron) + pruebas de rollover (old kid aún en JWKS).</li> <li>Abstracción de firma para usar KMS/HSM o key local.</li> <li>Documentación: flujos, secuencias, errores, mapping claims, ejemplos cURL/Postman.</li> </ul> <p>DoD</p> <ul> <li>Rollover sin downtime: tokens nuevos con <code>kid</code> nuevo, validación legacy operativa.</li> </ul>"}, {"location": "sprints/plan-sprints/#detalle-funcional-por-checklist-trazabilidad", "title": "Detalle funcional por checklist (trazabilidad)", "text": "<ul> <li>Infra: MariaDB, Redis, dominios/TLS LE, secretos .env → Sprint 0.</li> <li>DB + Prisma + seed oauth_clients → Sprints 0–1.</li> <li>OIDC Discovery + JWKS → Sprint 1.</li> <li>Auth Code + PKCE + tokens/rotation/revocation → Sprints 1–3.</li> <li>JWT con kid/aud/scope → Sprint 2.</li> <li>Middleware JWT + cache JWKS → Sprint 2.</li> <li>Logout SSO + back‑channel → Sprint 3.</li> <li>Gestión sesiones (FE) → Sprint 3.</li> <li>RBAC/permissions + consent → Sprint 4.</li> <li>Observabilidad (audit, métricas, traces) + rate limiting → Sprint 5.</li> <li>Rotación de claves (KMS/HSM) → Sprint 6.</li> <li>Swagger UI → Sprints 0–6 (in crescendo).</li> </ul>"}, {"location": "sprints/plan-sprints/#historias-por-sprint-resumen", "title": "Historias por sprint (resumen)", "text": "<p>S0</p> <ul> <li>Como dev, quiero levantar todo con <code>docker compose</code> y TLS.</li> <li>Como dev, quiero migrar y seedear DB en un comando.</li> </ul> <p>S1</p> <ul> <li>Como cliente, quiero descubrir capacidades OIDC (<code>/.well-known</code>).</li> <li>Como usuario, quiero iniciar sesión y obtener un <code>code</code> con PKCE.</li> </ul> <p>S2</p> <ul> <li>Como cliente, quiero canjear <code>code</code> por tokens (ID/Access/Refresh).</li> <li>Como API, quiero validar JWT con JWKS cacheado.</li> </ul> <p>S3</p> <ul> <li>Como usuario, quiero ver/cerrar mis sesiones y dispositivos.</li> <li>Como cliente, quiero recibir back‑channel logout y anular <code>sid</code>.</li> </ul> <p>S4</p> <ul> <li>Como admin, quiero asignar roles/permissions por cliente.</li> <li>Como usuario, quiero aceptar/rechazar scopes por cliente.</li> </ul> <p>S5</p> <ul> <li>Como SRE, quiero métricas/traces/logs con alertas.</li> <li>Como plataforma, quiero limitar abusos con rate limiting.</li> </ul> <p>S6</p> <ul> <li>Como seguridad, quiero rotar claves sin impacto.</li> <li>Como integrador, quiero docs/Swagger y ejemplos listos.</li> </ul>"}, {"location": "sprints/plan-sprints/#backlog-tecnico-crea-issues", "title": "Backlog técnico (crea issues)", "text": "<ul> <li>Plantillas de .env.example (todos los servicios) + <code>scripts/check-env.ts</code>.</li> <li>Comandos NPM: <code>dev</code>, <code>build</code>, <code>start</code>, <code>migrate</code>, <code>seed</code>, <code>lint</code>, <code>test</code>, <code>e2e</code>.</li> <li>SDK auth-mw exportando: expressMiddleware(), nestGuard(), jwksClient(cache), verifyJwt(aud,scope), requireScope().</li> <li>Playwright e2e: login, consent, token, refresh‑rotation, logout back‑channel.</li> <li>Dashboards Grafana (OIDC, Redis, DB).</li> <li>Scripts de data retention (tokens y logs antiguos).</li> </ul>"}, {"location": "sprints/plan-sprints/#variables-de-entorno-borrador-env", "title": "Variables de entorno (borrador .env)", "text": "<ul> <li><code>CEREBRO_ISSUER=https://auth.tu-dominio.com</code></li> <li><code>CEREBRO_JWKS_ALG=RS256</code> (o ES256)</li> <li><code>DATABASE_URL=mariadb://user:pass@maria:3306/cerebro</code></li> <li><code>REDIS_URL=redis://redis:6379/0</code></li> <li><code>COOKIE_SECURE=true</code> <code>COOKIE_SAMESITE=Lax</code></li> <li><code>TRUST_PROXY=1</code></li> <li><code>OTEL_EXPORTER_OTLP_ENDPOINT=http://otel-collector:4317</code></li> <li><code>RATE_LIMIT_AUTHZ=10/m</code> <code>RATE_LIMIT_TOKEN=20/m</code></li> </ul>"}, {"location": "sprints/plan-sprints/#criterios-de-calidad-globales", "title": "Criterios de calidad (globales)", "text": "<ul> <li>Seguridad: PKCE S256 obligatorio, SameSite+Secure, CSRF en FE, scopes mínimos, refresh rotation, back‑channel implementado, RBAC por cliente.</li> <li>Confiabilidad: pruebas unitarias + integración + e2e; rate limiting; circuit breakers para JWKS.</li> <li>Observabilidad: logs estructurados, métricas y traces con correlación.</li> <li>DX: Swagger vivo, ejemplos cURL, scripts de arranque y seeds.</li> <li>Docs: README por servicio + runbooks.</li> </ul>"}, {"location": "sprints/plan-sprints/#proximos-pasos", "title": "Próximos pasos", "text": "<ol> <li>Confirmar stack exacto (NestJS vs Express, monorepo vs multi‑repo).</li> <li>Crear repos + <code>infra/</code> (Sprint 0) y abrir issues del Sprint 0.</li> <li>Conectar tu base de React Vite a <code>cerebro-idp</code> (login/consent) desde S1.</li> </ol>"}]}